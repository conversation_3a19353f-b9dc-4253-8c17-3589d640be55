package keleshteri.clinic.management.laboratory.service.impl;

import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.laboratory.model.LaboratoryTest;
import keleshteri.clinic.management.laboratory.repository.LaboratoryTestRepository;
import keleshteri.clinic.management.laboratory.service.LaboratoryTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class LaboratoryTestServiceImpl implements LaboratoryTestService {

    @Autowired
    private LaboratoryTestRepository repository;

    @Override
    public LaboratoryTest find(Long id) {
        return repository.findById(id).orElseThrow(()->new ResourceNotFoundException("Test Not Find"));
    }

    @Override
    public LaboratoryTest find(String name) {
        return repository.findByName(name).orElseThrow(()->new ResourceNotFoundException("Test Not Find"));
    }

    @Override
    public List<LaboratoryTest> all() {
        return repository.findAll();
    }

    @Override
    public ResponseEntity<Page<LaboratoryTest>> pagination(Optional<Integer> size, Optional<Integer> page, Optional<String> sortBy, Sort.Direction order) {
        return null;
    }

    @Override
    public LaboratoryTest create(LaboratoryTest laboratoryTest) {
        return repository.save(laboratoryTest);
    }

    @Override
    public LaboratoryTest update(Long id, LaboratoryTest laboratoryTest) {
        return null;
    }

    @Override
    public Map<String, Boolean> delete(Long id) {
        return null;
    }

    @Override
    public void seeder() {
        if(repository.count()==0){
            LaboratoryTest testLFT = new LaboratoryTest("LFT (Liver function tests)","تست کبدی");
            LaboratoryTest testMorfin  = new LaboratoryTest("morfin test","تست مرفین");
            LaboratoryTest testMetadon = new LaboratoryTest("metadon test","تست متادون");
            LaboratoryTest testAmphetamin = new LaboratoryTest("amphetamin test","تست آمفتامین");
            LaboratoryTest testBuprenorphin = new LaboratoryTest("buprenorphin test","تست بوپره نورفین");

            repository.save(testLFT);
            repository.save(testMorfin);
            repository.save(testMetadon);
            repository.save(testAmphetamin);
            repository.save(testBuprenorphin);
        }
    }
}
