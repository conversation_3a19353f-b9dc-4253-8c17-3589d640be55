package keleshteri.clinic.management.pharmacy.product.payload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import keleshteri.clinic.management.pharmacy.medicine.enums.MedicineType;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.model.ProductUnitsConversion;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Data
public class ProductItemInventory {


    /** Product **/
    @JsonIgnore
    private Product product;

    /** InventoryItem **/
    @JsonIgnore
    private InventoryItem item;

    /** balance Inventory **/
    private Double balance;

    private Double balanceAdded=0.0;
    private Double balanceRemoved=0.0;


    /**
     * getter
     */
    /** ID Product **/
    public Long getId() {
        return this.product.getId();
    }
    //title
    public String getTitle() {
        return this.product.getName();
    }
    //price
    public BigDecimal getPrice() {
        //2 price
        return this.item.getSalePrice();
    }


    public void setItem(InventoryItem item) {
        this.item = item;
    }

    //inventory
    public Double getInventory() {
        return balance;
    }


    public String getMedicineName() {
        return product.getMedicine().getName();
    }

    public Integer getMedicineCode() {
        return product.getMedicine().getCode();
    }

    public String getTypeLabel() {
        return product.getTypeLabel();
    }

    public ProductUnitsConversion getUnitsConversion() {
        ProductUnitsConversion units =null;

        if(this.product.getType().equals(MedicineType.SYRUP)){
            System.out.println(product.getUnitsConversion().size());
            if(product.getUnitsConversion().size()>0)
            units=  product.getUnitsConversion()
                           .stream()
                           .filter(u->u.getToUnits().getCode().equals("cubic_centimetre"))
                           .findFirst()
                           .get();
        }else {
            units= product.getUnitsConversion().stream().findFirst().get();
        }

        return units;
    }

    //
//    public String getProductName() {
//        return product.getName();
//    }
//
//    public String getUnitsLabel(){
//       return units.getLabel();
//    }

//    quantity:2
//    total:NaN
//    units:"TABLET"

    /***
     * Constructor
     */

    public ProductItemInventory(
            Product product,
            InventoryItem item
    ) {
        this.product = product;
        this.item = item;
    }

    public ProductItemInventory(
            Product product,
            InventoryItem item,
            Double balance
            ) {
        this.product = product;
        this.item = item;
        this.balance=balance;
    }

    public ProductItemInventory(
            Product product,
            InventoryItem item,
            Double balance,
            Double balanceAdded,
            Double balanceRemoved
    ) {
        this.product = product;
        this.item = item;
        this.balance=balance;
        this.balanceAdded=balanceAdded;
        this.balanceRemoved=balanceRemoved;
    }
}
