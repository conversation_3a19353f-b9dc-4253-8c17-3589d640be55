package keleshteri.clinic.management.pharmacy.purchase.service.impl;

import keleshteri.clinic.management.exception.InvalidPurchaseException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.model.Supplier;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryVirtualService;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.service.ProductService;
import keleshteri.clinic.management.pharmacy.purchase.payload.PurchaseRequest;
import keleshteri.clinic.management.payment.service.SupplierService;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryItemService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryPeriodService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryOriginalService;
import keleshteri.clinic.management.pharmacy.purchase.model.Purchase;
import keleshteri.clinic.management.pharmacy.purchase.model.PurchaseItem;
import keleshteri.clinic.management.pharmacy.purchase.repository.PurchaseRepository;
import keleshteri.clinic.management.pharmacy.purchase.service.PurchaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class PurchaseServiceImpl implements PurchaseService {

    @Autowired
    private PurchaseRepository repository;

    @Autowired
    private InventoryPeriodService periodService;

    @Autowired
    private ProductService productService;

    @Autowired
    private InventoryOriginalService inventoryOriginalService;

    @Autowired
    private InventoryVirtualService inventoryVirtualService;

    @Autowired
    private InventoryItemService itemService;

    @Autowired
    private SupplierService supplierService;


    /**
     * find by id
     */
    @Override
    public Purchase find(Long id) {
        Purchase purchase = repository.findById(id)
                .orElseThrow(()-> new ResourceNotFoundException("خرید مورد نظر با این کد پیدا نشد"+id));
        return purchase;
    }

    @Override
    public ResponseEntity<List<Purchase>> pagination() {

        List<Purchase> purchases= repository.findAll();

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "transactions 0-"+purchases.size() + "/" + purchases.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(purchases);
    }

    @Override
    @Transactional//(rollbackFor = {Exception.class, InvalidPurchaseException.class})
    public Purchase update(Long id, PurchaseRequest request) throws InvalidPurchaseException  {
        /***
         * find period or create
         * InventoryPeriod
         * Get the active Period
         ***/
        InventoryPeriod period=periodService.find(request.getInventoryPeriodId());

        /***
         * find Supplier
         */
        Supplier supplier = supplierService.findFirstById(request.getSupplierId());

        /**
         * find the Purchase
         */
        Purchase editPurchase = this.find(id);

        editPurchase.setInvoiceDatePersian(request.getInvoiceDatePersian());
        editPurchase.setSupplier(supplier);
        editPurchase.setInventoryPeriod(period);
        editPurchase.setComments(request.getComments());

        //save
        Purchase purchase= repository.save(editPurchase);

        /***
         * create PurchaseItem
         */
        List<PurchaseItem> purchaseItems = new ArrayList<>();
        /******
         *convert itemRequest to   item
         ******/
        request.getItems().forEach(itemList->{
            /**
             * find product by id
             */
            Product product=productService.find(itemList.getProductId());
            /****
             * add item in PurchaseItem
             */
            purchaseItems.add(
                    new PurchaseItem(
                            purchase,//purchase
                            product,//product
                            itemList.getQuantity(),//quantity
                            itemList.getUnitPrice(),//unitPrice
                            itemList.getDiscount(),//discount
                            itemList.getSalePrice(),//salePrice
                            itemList.getSalePremiumPrice(),//SalePremiumPrice
                            itemList.getExpirationDate() //expirationDate
                    )
            );

        });
        /**
         * add purchaseItems
         */
        purchase.getItems().clear();
        purchase.getItems().addAll(purchaseItems);

        //save
        repository.save(purchase);
        //check this items on
        /****
         * add items on  Inventory
         ****/
        purchase.getItems().forEach(phItem->{
            /*****
             *** find inventory Item or create
             *** if PURCHASE let you create items
             ****/
            InventoryItem inventoryItem= itemService.firstOrCreate(
                    phItem.getProduct(),
                    period,
                    phItem.getUnitPrice(),
                    phItem.getSalePrice(),
                    phItem.getSalePremiumPrice(),
                    phItem.getExpirationDate(),
                    phItem.getQuantity()
            );
            //save each
//            inventoryService.all(purchase).forEach(mm->{
//                System.out.println(mm.getQuantity());
//            });
//            System.out.println(inventoryService.all(purchase).size());

//            inventoryService.create(new InventoryOriginal(
//                    InventoryType.ADDITION,
//                    inventoryItem,
//                    purchase,
//                    phItem.getQuantity(),
//                    phItem.getTotal(),
//                    purchase.getInvoiceDatePersian()
//            ));
        });

        System.out.println(10/0);

        return purchase;
    }


    /***
     * Create Purchase
     * @throws InvalidPurchaseException
     */
    @Override
    @Transactional//(rollbackFor = {Exception.class, InvalidPurchaseException.class})
    public Purchase create(PurchaseRequest request) throws InvalidPurchaseException {


        /***
         * find period or create
         * InventoryPeriod
         * Get the active Period
         ***/
        InventoryPeriod period=periodService.find(request.getInventoryPeriodId());

        /***
         * find Supplier
         */
        Supplier supplier = supplierService.findFirstById(request.getSupplierId());

        /***
         * create  Purchase
         ***/
        Purchase NewPurchase = new Purchase(
                request.getInvoiceDatePersian(),
                supplier,
                period,
                request.getComments()
        );
        //save
        Purchase purchase= repository.save(NewPurchase);

        /***
         * create PurchaseItem
         */
        List<PurchaseItem> purchaseItems = new ArrayList<>();
        /******
         *convert itemRequest to   item
         ******/
        request.getItems().forEach(itemList->{
            /**
             * find product by id
             */
            Product product=productService.find(itemList.getProductId());
            /****
             * add item in PurchaseItem
             */
            purchaseItems.add(
                    new PurchaseItem(
                            purchase,//purchase
                            product,//product
                            itemList.getQuantity(),//quantity
                            itemList.getUnitPrice(),//unitPrice
                            itemList.getDiscount(),//discount
                            itemList.getSalePrice(),//salePrice
                            itemList.getSalePremiumPrice(),//SalePremiumPrice
                            itemList.getExpirationDate() //expirationDate
                    )
            );

        });
        /**
         * add purchaseItems
         */
        purchase.getItems().addAll(purchaseItems);
        //save
        repository.save(purchase);
        /****
         * add items on  Inventory
         ****/
        //save in Original
        inventoryOriginalService.addItem(purchase);
        //save in Virtual
        inventoryVirtualService.addItem(purchase);

        return purchase;
    }



}
