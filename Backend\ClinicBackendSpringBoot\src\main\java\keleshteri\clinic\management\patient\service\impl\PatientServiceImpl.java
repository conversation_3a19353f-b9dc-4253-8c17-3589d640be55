package keleshteri.clinic.management.patient.service.impl;

import keleshteri.clinic.management.exception.RecordExistsException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.patient.helper.PatientExcelHelper;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.patient.repository.PatientRepository;
import keleshteri.clinic.management.patient.service.PatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Service
public class PatientServiceImpl implements PatientService {

    private final PatientRepository patientRepository;

    @Autowired
    public PatientServiceImpl(PatientRepository patientRepository) {
        this.patientRepository = patientRepository;
    }


    @Override
    public ResponseEntity<Page<Patient>> pagination2(Optional<Integer> size, Optional<Integer> page, Optional<String> sortBy,Sort.Direction  order) {

        Page<Patient> patients = patientRepository.findAll(
                PageRequest.of(
                        page.orElse(0),
                        size.orElse(5),
                        order ,
                        sortBy.orElse("id")
                )
        );

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "patients 0-"+patients.getTotalPages() + "/" + patients.getTotalPages());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(patients);
    }

    public Patient findFirst(Long id){
        Patient patient= patientRepository.findById(id)
                .orElseThrow(()-> new ResourceNotFoundException("patient not exist with id :" + id));
        return patient;
    }


    public List<Patient> findAllPatients() {
        return patientRepository.findAll();
    }

    @Override
    public List<Patient> all() {
        return patientRepository.findAll();
    }

    @Override
    public ResponseEntity<Patient> find(Long id) {
        Patient patient = patientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Patient not exist with id :" + id));
        return ResponseEntity.ok(patient);
    }

    @Override
    public ResponseEntity<List<Patient>> pagination() {
        //get all list
        List<Patient> patient= patientRepository.findAll();

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "patients 0-"+patient.size() + "/" + patient.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(patient);
    }
    @Override
    public ResponseEntity<List<Patient>> paginationWithOutDeleted() {
        //get all list
        List<Patient> patient= patientRepository.findByDeleted(false);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "patients 0-"+patient.size() + "/" + patient.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(patient);
    }

    @Override
    public ResponseEntity<Patient> create(Patient patient) {
        //check if findByFileNumber
        boolean existsFileNumber = patientRepository.existsByFileNumber(patient.getFileNumber());
        if(existsFileNumber){
            throw  new RecordExistsException("Patient exist with FileNumber: "+ patient.getFileNumber());
        }
        //exists nationalId
        boolean existsNationalId = patientRepository.existsByNationalId(patient.getNationalId());
        if(existsNationalId){
            throw  new RecordExistsException("Patient exist with NationalId: "+ patient.getNationalId());
        }


//                .orElseThrow(() -> new ResourceNotFoundException("Patient not exist with id :" ));
//        try{
        return ResponseEntity.ok(patientRepository.save(patient));
//            return new ResponseEntity<Patient>(HttpStatus.FORBIDDEN);
//        }
//        catch (DataIntegrityViolationException e){
//            System.out.println("history already exist");
//            System.out.println(e.getMessage());
//            System.out.println(e);
////            throw new IllegalStateException("email already confirmed");
//        }
//        finally {
//            return new ResponseEntity<Patient>(HttpStatus.NOT_FOUND) ;
//        }
//        catch (javax.validation.ConstraintViolationException cve){
//
//            return new ResponseEntity<Patient>(HttpStatus.NOT_FOUND) ;
//        }

    }

    @Override
    public ResponseEntity<Patient> update(Long id, Patient patientDetails) {
        System.out.println("update-Patient");
        //check if patient id
        Patient patient = patientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Patient not exist with id :" + id));
        //set for update
          //FileNumber 1
        patient.setFileNumber(patientDetails.getFileNumber());
          //National 2
        patient.setNationalId(patientDetails.getNationalId());
          //FirstName 3
        patient.setFirstName(patientDetails.getFirstName());
          //LastName 4
        patient.setLastName(patientDetails.getLastName());
           //FileDate 5
        patient.setFileDate(patientDetails.getFileDate());
           //Day 6
        patient.setDay(patientDetails.getDay());
          //Gender 7
        patient.setGender(patientDetails.getGender());
          //BirthDatePersian 8
        patient.setBirthDatePersian(patientDetails.getBirthDatePersian());
          //Email 9
        patient.setEmail(patientDetails.getEmail());
          //CellphoneNumber 10
        patient.setCellphoneNumber(patientDetails.getCellphoneNumber());
          //LandlineNumberHome 11
        patient.setLandlineNumberHome(patientDetails.getLandlineNumberHome());
          //LandlineNumberOffice 12
        patient.setLandlineNumberOffice(patientDetails.getLandlineNumberOffice());
          //HomeAddress 13
        patient.setHomeAddress(patientDetails.getHomeAddress());
         //Confirm 14
        patient.setConfirm(patientDetails.isConfirm());
        //Deleted
        patient.setDeleted(patientDetails.getDeleted());

        //patient save  data or update
        Patient updatedPatient = patientRepository.save(patient);

        return ResponseEntity.ok(updatedPatient);
    }

    @Override
    public ResponseEntity<Map<String, Boolean>> delete(Long id) {
        Patient patient = patientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Patient not exist with id :" + id));

        patient.setDeleted(true);
//        patientRepository.delete(patient);
        patientRepository.save(patient);
        Map<String, Boolean> response = new HashMap<>();
        response.put("result", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }

    /**
     * confirm
     */
    @Override
    public ResponseEntity<Map<String, Boolean>> confirm(Long id) {
        //patient
        Patient patient = patientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Patient not exist with id :" + id));

        //patient
        boolean data=  !patient.isConfirm();//((patient.getConfirm() == true) ? true : false);

        patient.setConfirm(data);
        patientRepository.save(patient);
        //response
        Map<String, Boolean> response = new HashMap<>();
        response.put("result", Boolean.TRUE);
        //
        return ResponseEntity.ok(response);

    }




    @Override
    public ResponseEntity<Map<String, Boolean>> existsFileNumber(Long fileNumber) {
         Map<String,Boolean> response = new HashMap<>();

         //set value on exist boolean
         response.put("exists",this.patientRepository.existsByFileNumber(fileNumber));

         return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<Map<String, Boolean>> existsNtionalId(String nationalId) {
        Map<String,Boolean> response = new HashMap<>();

        //set value on exist boolean
        response.put("exists",this.patientRepository.existsByNationalId(nationalId));

        return ResponseEntity.ok(response);
    }

    //saveByExcel
    public void saveByExcel(MultipartFile file) {
        try {
            List<Patient> patients = PatientExcelHelper.excelToPatients(file.getInputStream());
            patientRepository.saveAll(patients);
        } catch (IOException e) {
            throw new RuntimeException("fail to store excel data: " + e.getMessage());
        }
    }

    @Override
    public Long countPatient() {
        return patientRepository.count();
    }
}
