package keleshteri.clinic.management.pharmacy.virtual;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@Data
public class SelectOptions   {

   private Long company;

   private Long units;

   private List<Variant> variantValue;

//   public ProductVariant getProductVariant(Product product) {
//      ProductVariant productVariant = new ProductVariant();
//      productVariant.setProduct(product);
//      productVariant.setCompany();
//      productVariant.setUnits();
//      productVariant.setName();
//      return productVariant;
//   }
}
