package keleshteri.clinic.management.pharmacy.delivery.service;

import keleshteri.clinic.management.pharmacy.delivery.model.DeliveryDetails;
import keleshteri.clinic.management.pharmacy.delivery.repository.DeliveryDetailsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeliveryDetailsService {

    private final DeliveryDetailsRepository repository;

    @Autowired
    public DeliveryDetailsService(DeliveryDetailsRepository repository) {
        this.repository = repository;
    }

    public DeliveryDetails create(DeliveryDetails deliveryDetails){
        return repository.save(deliveryDetails);
    }
}
