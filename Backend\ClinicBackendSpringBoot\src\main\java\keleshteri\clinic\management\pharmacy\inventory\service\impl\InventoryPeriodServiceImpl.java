package keleshteri.clinic.management.pharmacy.inventory.service.impl;

import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.exception.InventoryException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.repository.InventoryPeriodRepository;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryPeriodService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 8/21/2021
 */
@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class InventoryPeriodServiceImpl implements InventoryPeriodService {

    private final InventoryPeriodRepository repository;

    //find
    public InventoryPeriod find(Long id){
        InventoryPeriod period = repository.findById(id)
                .orElseThrow(()-> new ResourceNotFoundException("دور مورد نظر پیدا نشد"));
        return period;
    }

    //find
    public InventoryPeriod find(String startDate,boolean finish){
        InventoryPeriod period =
                repository.findByFinishAndStartDatePersianGreaterThan( finish,startDate)
                        .orElseThrow(
                                ()-> new ResourceNotFoundException(" این دوره با شروع این تاریخ نیست " + startDate)
                        );
        return period;
    }
    //all
    public List<InventoryPeriod> all(){
        List<InventoryPeriod> inventoryPeriods= repository.findAll();
        return inventoryPeriods;
    }
    //find item or create
    public InventoryPeriod firstOrCreate(String startDate){
        InventoryPeriod period = repository.findByStartDate(startDate)
                .orElse(repository.save(
                        new InventoryPeriod(startDate)
                ));
        return period;
    }
    //find item or create
    public InventoryPeriod firstOrCreate(String startDate,String endDate){
        InventoryPeriod period = repository.findByStartDatePersianAndEndDatePersian(startDate,endDate)
                .orElse(repository.save(
                        new InventoryPeriod(startDate,endDate)
                ));
        return period;
    }
    //find item or create
    public InventoryPeriod firstOrCreate(String startDate,String endDate,boolean finish){
        InventoryPeriod period = repository.findByStartDatePersianAndEndDatePersianAndFinish(startDate,endDate,finish)
                .orElse(repository.save(
                        new InventoryPeriod(startDate,endDate,finish)
                ));
        return period;
    }

    //
    public InventoryPeriod create(InventoryPeriod period){
        //
        //if han in period date
        if( repository.existsInventoryPeriodByStartDateLessThanEqualAndEndDateGreaterThanEqual(period.getEndDate(),period.getStartDate())){
            throw new InventoryException("این دور تاریخ قبلا وارد شده");
        }
        return repository.save(period);
    }


    public List<InventoryPeriod> findAllWithStartDateAfter(String startDate,boolean finish){
        PersianDate persianDate = PersianDate.parse(startDate);
        LocalDate DateConverted = persianDate.toGregorian();
        return repository.findAllWithStartDateAfter(DateConverted,finish);
    }

    public boolean existsPeriodByDate(String startDate,boolean finish){
        PersianDate persianDate = PersianDate.parse(startDate);
        LocalDate DateConverted = persianDate.toGregorian();
        return repository.existsPeriodByDate(DateConverted,finish);
    }

    public boolean existsPeriodByDates(LocalDate startDate,LocalDate endDate,boolean finish){
        return repository.existsInventoryPeriodByStartDateLessThanEqualAndEndDateGreaterThanEqualAndFinish(endDate,startDate,finish);
    }



    //Has any period not finish
    //Has date of add or remove in period
    public InventoryPeriod acceptableDate(String startDate){

        PersianDate persianDate = PersianDate.parse(startDate);
        LocalDate DateConverted = persianDate.toGregorian();

//        InventoryPeriod period = repository.findByFinishAndStartDateGreaterThan(false,date).orElseThrow(()->new ResourceNotFoundException("not found"));
        InventoryPeriod period = repository.findByFinishAndStartDateGreaterThan(false,DateConverted).orElseThrow(()->new ResourceNotFoundException("not found"));
        return period;
    }
}
