package keleshteri.clinic.management.repository;

import keleshteri.clinic.management.product_generics.model.ProductGeneric;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProductGenericRepository extends JpaRepository<ProductGeneric,Long> {


    Optional<ProductGeneric> findByName(String name);
}
