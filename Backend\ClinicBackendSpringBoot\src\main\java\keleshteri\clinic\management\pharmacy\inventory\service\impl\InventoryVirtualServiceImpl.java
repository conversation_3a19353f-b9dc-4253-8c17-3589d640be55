package keleshteri.clinic.management.pharmacy.inventory.service.impl;

import keleshteri.clinic.management.exception.InventoryException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.enums.InventoryType;
import keleshteri.clinic.management.pharmacy.delivery.model.Delivery;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryOriginal;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryVirtual;
import keleshteri.clinic.management.pharmacy.inventory.repository.InventoryVirtualRepository;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryItemService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryVirtualService;
import keleshteri.clinic.management.pharmacy.purchase.model.Purchase;
import keleshteri.clinic.management.pharmacy.virtual.InventoryBalance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

/**
 * <AUTHOR> Mehdi Shaban keleshteri
 * @version 1.0
 * @since 8/20/2021
 */
@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class InventoryVirtualServiceImpl implements InventoryVirtualService {

    private final InventoryVirtualRepository inventoryRepo;
    private final InventoryItemService inventoryItemService;

    /***
     * find a InventoryOriginal by id
     ***/
    @Override
    public InventoryVirtual find(Long id) {
        InventoryVirtual inventory = inventoryRepo.findById(id)
                .orElseThrow(()-> {
                    log.error("Inventory not found in the database");
                    return   new ResourceNotFoundException("این مورد در انبار پیدا نشد کد:" + id);
                });
        return  inventory;
    }

    /**
     * List InventoryOriginal
     */
    @Override
    public List<InventoryVirtual> all() {
        List<InventoryVirtual> inventoryVirtuals = inventoryRepo.findAll();
        return inventoryVirtuals;
    }

    /**
     * List InventoryOriginal by Period
     */
    @Override
    public List<InventoryVirtual> all(InventoryPeriod period) {
        List<InventoryVirtual> inventoryVirtuals= inventoryRepo.findByPeriod(period);
        return inventoryVirtuals;
    }

    /**
     * Pagination Original
     */
    @Override
    public ResponseEntity<Page<InventoryVirtual>> Pagination(Optional<Integer> size, Optional<Integer> page, Optional<String> sortBy, boolean desc) {
        Page<InventoryVirtual> inventoryVirtualPage = inventoryRepo.findAll(
                PageRequest.of(
                        page.orElse(0),
                        size.orElse(5),
                        desc==true? Sort.Direction.DESC:Sort.Direction.ASC,
                        sortBy.orElse("id")
                )
        );
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "inventories 0-"+inventoryVirtualPage.getTotalPages() + "/" + inventoryVirtualPage.getTotalPages());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(inventoryVirtualPage);
    }

    /**
     * Pagination Original by InventoryPeriod
     */
    @Override
    public ResponseEntity<Page<InventoryVirtual>> Pagination(InventoryPeriod period, Optional<Integer> size, Optional<Integer> page, Optional<String> sortBy, boolean desc) {
        Page<InventoryVirtual> inventoryVirtualPage = inventoryRepo.findByPeriod(
                period,
                PageRequest.of(
                        page.orElse(0),
                        size.orElse(5),
                        desc==true? Sort.Direction.DESC:Sort.Direction.ASC,
                        sortBy.orElse("id")
                )
        );
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "inventories 0-"+inventoryVirtualPage.getTotalPages() + "/" + inventoryVirtualPage.getTotalPages());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(inventoryVirtualPage);
    }

    @Override
    public InventoryVirtual create(InventoryVirtual inventoryVirtual) {
       log.info("save inventory");
        return inventoryRepo.save(inventoryVirtual);
    }

    @Override
    public InventoryVirtual update(Long id, InventoryVirtual inventoryDetails) {
        //find the InventoryOriginal
        InventoryVirtual inventory= this.find(id);
        //Set new data to old data

        //Source
        inventory.setSource(inventoryDetails.getSource());
        //InventoryType
        inventory.setInventoryType(inventoryDetails.getInventoryType());
        //InventoryItem
        inventory.setInventoryItem(inventoryDetails.getInventoryItem());
        //Quantity
        inventory.setQuantity(inventoryDetails.getQuantity());
        //on hold this for before add or remove from Inventory
        inventory.setOnHold(inventoryDetails.isOnHold());
        //Price
        inventory.setPrice(inventoryDetails.getPrice());
        //datePersian;
        inventory.setDatePersian(inventoryDetails.getDatePersian());
        //DateEnglish
        inventory.setDate(inventoryDetails.getDate());

        //save the change
        InventoryVirtual saveInventory = inventoryRepo.save(inventory);

        return  saveInventory;
    }

    @Override
    public Map<String, Boolean> delete(Long id) {
        //find
        InventoryVirtual inventory= this.find(id);
        //delete
        inventoryRepo.save(inventory);

        /** response **/
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);

        return response;
    }



    @Override
    public void addItem(Purchase purchase) {
        log.info("start to save {} items  of purchase in Virtual",purchase.getItems().size());
        // List of InventoryOriginal
        List<InventoryVirtual> inventoryVirtuals = new ArrayList<>();
        //forEach to get items
        purchase.getItems().forEach(item->{
            /*****
             *** find inventory Item or create
             *** if PURCHASE let you create items
             ****/
            InventoryItem inventoryItem= inventoryItemService.firstOrCreate(
                    item.getProduct(),
                    purchase.getInventoryPeriod(),
                    item.getUnitPrice(),
                    item.getSalePrice(),
                    item.getSalePremiumPrice(),
                    item.getExpirationDate(),
                    item.getQuantity()
            );
            log.info("find or create InventoryItem {}", inventoryItem.getProduct().getName());


            //add to list inventoryOriginals
            inventoryVirtuals.add(
                    new InventoryVirtual(
                            InventoryType.ADDITION,
                            inventoryItem,
                            purchase,
                            item.getQuantity(),
                            item.getTotal(),
                            purchase.getInvoiceDatePersian()
                    )
            );
        });
        //save all the list
        inventoryRepo.saveAll(inventoryVirtuals);
    }

    @Override
    public void removeItem(Delivery delivery) {

    }

    @Override
    public void returnItem(Delivery delivery) {

    }

    /***
     *
     */
    public InventoryVirtual save(InventoryVirtual inventoryVirtual) {
        //check if REMOVAL
        if(inventoryVirtual.getInventoryType().equals(InventoryType.REMOVAL)){
            //check if Inventory has the Items
            if(inventoryRepo.existsByInventoryItem(inventoryVirtual.getInventoryItem())==false){
                throw new InventoryException(
                        " محصول مورد نظر در انبار ثبت نشده است(ورودی و خروجی) "+
                                inventoryVirtual.getInventoryItem().getProduct().getName());
            }//end check Inventory has the Items

            //check if Inventory has the Items with InventoryTypeADDITION
            if(inventoryRepo.existsByInventoryItemAndInventoryType(inventoryVirtual.getInventoryItem(),InventoryType.ADDITION)==false){
                throw new InventoryException(
                        " محصول مورد نظر در انبار ثبت نشده است(ورودی) این مورد به ادمین گزارش دهید"+
                                inventoryVirtual.getInventoryItem().getProduct().getName());
            }//end check Inventory has the Items with InventoryTypeADDITION

            //check if Quantity is null
            if(inventoryVirtual.getQuantity()==null){
                throw new InventoryException("لطفا تعداد را وارد کنید");
            }

            //check if Quantity is 0
            if(inventoryVirtual.getQuantity()==0){
                throw new InventoryException("0 تعداد مجاز نیست");
            }


            //check the Product has Stock
            //Option 1
            //First get the ADDITION and Removal
            Double stock  = this.stockBalance(inventoryVirtual.getInventoryItem());
            Double stock2 = this.stockBalance2(inventoryVirtual.getInventoryItem());
            //check if Option 1 and Option 2 the same or no
            if(stock.equals(stock2)==false){
                throw new InventoryException("انبار دچار اشتباه محاسباتی شده هست لطفا به ادمین گزارش کنید");
            }
            //check stock more than 0
            if(stock<=0){
                throw  new InventoryException("موجودی محصول مورد نظر صفر شده");
            }
            //check the request qty more than balance
            if(inventoryVirtual.getQuantity().doubleValue()>stock.doubleValue()){
                throw new InventoryException("درخواست شما بیشتر از موجودی انبار هست موجودی انبار :"+stock+" ");
            }
            System.out.println(inventoryVirtual.getQuantity().doubleValue()+">"+stock.doubleValue());
            System.out.println(inventoryVirtual.getQuantity().doubleValue()+">"+stock.doubleValue());
            ///
            //add convert Quantity to   negative value
            //  Quantity is greater than 0 is a positive number.
            if(inventoryVirtual.getQuantity()>0){
                inventoryVirtual.setQuantity(-(inventoryVirtual.getQuantity()));
            }



        }//end REMOVAL

        //save  inventory
        return inventoryRepo.save(inventoryVirtual);
    }




    /**
     * inventoryVirtual
     */
    /***
     * check the Product has Stock
     * @param inventoryItem
     * @return
     */
    @Override
    public Double stockBalance(InventoryItem inventoryItem){
        //First get the ADDITION and Removal
        Double balanceAddition = inventoryRepo.sumByProductQuantity(inventoryItem, InventoryType.ADDITION);
        Double balanceRemoval  = inventoryRepo.sumByProductQuantity(inventoryItem, InventoryType.REMOVAL);
        //check if null
        balanceAddition = balanceAddition==null? 0:balanceAddition;
        balanceRemoval  = balanceRemoval == null? 0:balanceRemoval;
        //calculator for stock
        Double stock =balanceAddition+balanceRemoval;
        return stock;
    }
    /**
     * check the Product has Stock
     * @param inventoryItem
     * @return
     */
    @Override
    public Double stockBalance2(InventoryItem inventoryItem){
        //Get InventoryProductBalance by grouping InventoryType
        List<InventoryBalance> balance =this.itemBalances(inventoryItem);
        //calculator for stock
        Double stock= balance.stream()
                .mapToDouble(b->b.getBalance().doubleValue())
                .reduce(0, InventoryOriginal::add);
        return stock;
    }

    /**
     * check item saved in Inventory
     */
    @Override
    public boolean existsItem(InventoryItem item) {
        //check if Inventory has the Items
        return inventoryRepo.existsByInventoryItem(item);
    }

    /**
     * check item saved in Inventory
     */
    @Override
    public boolean existsItem(
            InventoryItem item,
            InventoryType type) {
        //check if Inventory has the Items with InventoryTypeADDITION
        return inventoryRepo.existsByInventoryItemAndInventoryType(item,type);
    }

    /**
     * check item saved in Inventory
     * InventoryException
     */
    @Override
    public void existsItemException(InventoryItem item) throws InventoryException {
        //check if Inventory has the Items
        if(this.existsItem(item)==false){
            throw new InventoryException(
                    " محصول مورد نظر در انبار ثبت نشده است(ورودی و خروجی) "+
                            item.getProduct().getName());
        }//end check Inventory has the Items
    }

    /**
     * get inventoryOriginal inventoryItem Balance by Type
     * @param inventoryItem
     * @return
     */
    public List<InventoryBalance> itemBalances(InventoryItem inventoryItem){
        //Get InventoryProductBalance by grouping InventoryType
        List<InventoryBalance> itemBalance =inventoryRepo.sumQuantityGroupType(inventoryItem);
        return  itemBalance;
    }
}
