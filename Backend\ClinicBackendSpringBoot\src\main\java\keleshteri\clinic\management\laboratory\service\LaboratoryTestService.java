package keleshteri.clinic.management.laboratory.service;

import keleshteri.clinic.management.laboratory.model.LaboratoryTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface LaboratoryTestService {

    /**
     * find by id
     */
    LaboratoryTest find(Long id);

    /**
     * find by name
     */
    LaboratoryTest find(String name);

    /**
     * all LaboratoryTest
     */
    List<LaboratoryTest> all();

    /**
     * pagination
     */
    ResponseEntity<Page<LaboratoryTest>> pagination(
            Optional<Integer> size,
            Optional<Integer> page,
            Optional<String> sortBy,
            Sort.Direction order
    );

    /**
     * Create a New LaboratoryTest
     */
    LaboratoryTest create(LaboratoryTest laboratoryTest);

    /**
     * Update a LaboratoryTest
     */
     LaboratoryTest update(Long id,LaboratoryTest laboratoryTest);

    /**
     * Delete a
     */
    Map<String, Boolean> delete(Long id);

    /**
     * seeder
     */
    void seeder();
}
