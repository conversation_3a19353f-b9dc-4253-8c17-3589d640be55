package keleshteri.clinic.management.pharmacy.sale.repository;


import keleshteri.clinic.management.payment.interfaces.SourceClient;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.sale.model.Sale;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface SaleRepository extends JpaRepository<Sale,Long> {

    List<Sale> findByPeriod(InventoryPeriod period);

    List<Sale> findByInvoiceDateBetween(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
            );


    Page<Sale> findByInvoiceDateBetween(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            Pageable pageable);

    @Modifying
    @Query(value = "DELETE FROM sales WHERE id = :saleId", nativeQuery = true)
    void deleteSaleById(@Param("saleId") Long saleId);

//    List<Sale> findAllByInvoiceDateLessThanEqualAndInvoiceDateGreaterThanEqual(LocalDate invoiceDate,LocalDate invoiceDate);

//    @Query(value = "select * from Sale s " +
//            "where s.InvoiceDate BETWEEN :startDate AND :endDate")
//    Page<Sale> getAllBetweenDates(
//            @Param("startDate")LocalDate startDate,
//            @Param("endDate")LocalDate endDate,
//            Pageable pageable
//            );

//    /**  Sale  ByPeriod **/
//    @Query(value = "SELECT p " +
//            "from Sale p, " +
//            "where  p.period= :period ",
//            nativeQuery = true
//    )
//    List<Sale> getSalesByPeriod(
//            @Param("period")InventoryPeriod period
//    );

//    Sale findOneBySourceClient_clientId(@Param("encounterId") Long encounterId)
//    Sale findOneByClientId(@Param("clientId") Long client);

//    @Query(
//            value = "SELECT * FROM Sale s   "
////            "WHERE s.period=:period "
////            "AND (d.type='SALES_INVOICE' OR d.type='SALES_RECEIPT')"
//            )
//    List<Sale> getList();

//    List<Sale> getLastPurchasedFrames(@Param("period") InventoryPeriod period);



}
