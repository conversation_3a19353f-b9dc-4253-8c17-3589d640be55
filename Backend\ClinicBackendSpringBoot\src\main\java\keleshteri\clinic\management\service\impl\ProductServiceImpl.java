package keleshteri.clinic.management.service.impl;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.enums.MathematicalSign;
import keleshteri.clinic.management.product_generics.model.ProductGeneric;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.medicine.enums.MedicineType;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.pharmacy.medicine.service.MedicineService;
import keleshteri.clinic.management.model.Company;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.model.ProductUnitsConversion;
import keleshteri.clinic.management.pharmacy.product.payload.ProductItemInventory;
import keleshteri.clinic.management.repository.ProductRepository;
import keleshteri.clinic.management.service.CompanyService;
import keleshteri.clinic.management.service.ProductGenericService;
import keleshteri.clinic.management.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {


    private final ProductRepository repository;

    @Autowired
    private ProductGenericService productGenericService;

    @Autowired
    private MedicineService medicineService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    public ProductServiceImpl(ProductRepository repository) {
        this.repository = repository;
    }


    @Override
    public Product find(Long id) {
        return repository.findById(id)
                .orElseThrow(()->new ResourceNotFoundException("محصول با این کد وجود نداشت :" + id));
    }

    @Override
    public Product find(String name) {
        return repository.findByName(name)
                .orElseThrow(()-> new ResourceNotFoundException("محصول با این نام وجود نداشت :" + name));
    }

    @Override
    public List<Product> all() {
        return repository.findAll();
    }

    /** get all by products In Item By period **/
    public List<Product> productItemsByPeriod(InventoryPeriod period){
        return  repository.getProductItemsByPeriod(period);
    }

    /**
     * all product in items with Inventory
     */
    public List<ProductItemInventory> items(){
        return repository.getProductItemsInventory();
    }

    /**
     * productItemsInventoryByPeriod
     */
    public List<ProductItemInventory> productItemsInventoryByPeriod(InventoryPeriod period) {
        log.info("ProductService: ProductItemInventory");
        List<ProductItemInventory> list=repository.getProductItemsInventoryByPeriod(period);
//        log.debug("products",list);
        log.info("products size  {}",list.size());
        list.forEach(l->{
            log.info(" product:  {}",l);
        });
        return list;
    }

    /**
     * productItemsInventoryVirtualByPeriod
     */
    public List<ProductItemInventory> productItemsInventoryVirtualByPeriod(InventoryPeriod period) {
        return repository.getProductItemsInventoryVirtualByPeriod(period);
    }

    @Override
    public ResponseEntity<List<Product>> pagination() {
        //get all
        List<Product> products = this.all();
        //set HttpHeaders
        HttpHeaders responseHeaders = new HttpHeaders();
        //set Content-Range
        responseHeaders.set("Content-Range", "products 0-"+products.size() + "/" + products.size());
        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(products);
    }

    @Override
    public Product create(Product product) {
        /**
         * if no code generate code
         */
        if(product.getCode()==null){
            if(repository.count()==0){
                product.setCode(900);
            }else {
                product.setCode(repository.getMaxCode()+1);
            }
        }

        return repository.save(product);
    }

    @Override
    public Product update(Long id, Product productDetails) {
        return null;
    }

    @Override
    public Map<String, Boolean> delete(Long id) {
        /**find  product **/
        Product product =this.find(id);
        /** Deleted **/
        repository.delete(product);
        /** response **/
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);

        return response;
    }

    @Override
    public void seeder() {
        if(repository.count()==0){
            //find Medicine
            //Medicine
            Medicine methadone = medicineService.find("متادون");
            Medicine opium = medicineService.find("اپیوم");
            Medicine buprenorphine = medicineService.find("بوپرنورفین");
            Medicine suboxone = medicineService.find("سابوکسون");

            //MedicineCompany
            Company DarouPakhsh = companyService.find("داروپخش");
            Company Zagrosdarou = companyService.find("زاگرس");
            Company DarouDarmanPars =companyService.find("دارو درمان پارس");
            Company MehrDarou=companyService.find("مهر دارو");
            Company Exir =companyService.find("اکسیر");
            Company Faran=companyService.find("فاران");
            Company FaranShimi=companyService.find("فاران شیمی");
            Company Temad=companyService.find("تماد");

            //Color
            String White =  "سفید";
            String Brown = "قهوه ای";

            //
            ProductGeneric methadone5= productGenericService.find("متادون 5");
            ProductGeneric methadone20= productGenericService.find("متادون 20");
            ProductGeneric methadone40= productGenericService.find("متادون 40");
            ProductGeneric methadoneSyrup= productGenericService.find("شربت متادون");
            ProductGeneric opiumSyrup= productGenericService.find("شربت اپیوم");
            ProductGeneric buprenorphineTablet= productGenericService.find("بوپرنورفین");
            ProductGeneric suboxoneTablet= productGenericService.find("سابوکسون");


            /***
             * methadone5 Product
             */
            //methadone5DarouPakhsh
            Product methadone5DarouPakhsh  = new Product(
                    900,//code
                    methadone5,//genericName
                    methadone5.getName()+"-"+DarouPakhsh.getName(), //  name,
                    DarouPakhsh,//company
                    methadone,//Medicine
                    MedicineType.TABLET//medicineType
            );

            //methadone5Zagrosdarou
            Product methadone5Zagrosdarou  = new Product(
                    901,
                    methadone5,//genericName
                    methadone5.getName()+"-"+Zagrosdarou.getName(),
                    DarouPakhsh,//company
                    methadone,
                    MedicineType.TABLET);
            /***
             * Product methadone20
             */
            //methadone20DarouPakhshWhite
            Product methadone20DarouPakhshWhite = new Product(
                    902,
                    methadone20,
                    methadone20.getName()+"-"+DarouPakhsh.getName()+"-"+White,
                    DarouPakhsh,//company
                    methadone,//
                    MedicineType.TABLET);
            //
            Product methadone20ZagrosdarouWhite = new Product(
                    903,
                    methadone20,
                    methadone20.getName()+"-"+Zagrosdarou.getName()+"-"+White,
                    Zagrosdarou,//company
                    methadone,//
                    MedicineType.TABLET);
            //methadone20MehrDarouBrown
            Product methadone20MehrDarouBrown= new Product(
                    904,
                    methadone20,
                    methadone20.getName()+"-"+MehrDarou.getName()+"-"+Brown,
                    MehrDarou,
                    methadone,
                    MedicineType.TABLET
            );
            //methadone20BrownDarouDarmanPars
            Product methadone20BrownDarouDarmanPars= new Product(
                    905,
                    methadone20,
                    methadone20.getName()+"-"+DarouDarmanPars.getName()+"-"+Brown,
                    DarouDarmanPars,
                    methadone,
                    MedicineType.TABLET
            );
            /***
             * Product methadone40
             */

            //methadone40DarouPakhshWhite
            Product methadone40DarouPakhshWhite = new Product(
                    906,
                    methadone40,
                    methadone40.getName()+"-"+DarouPakhsh.getName()+"-"+White,
                    DarouPakhsh,
                    methadone,MedicineType.TABLET
            );

            //methadone40MehrDarouBrown
            Product methadone40MehrDarouBrown = new Product(
                    907,
                    methadone40,
                    methadone40.getName()+"-"+MehrDarou.getName()+"-"+Brown,
                    MehrDarou,
                    methadone,
                    MedicineType.TABLET
            );

            //methadone40ExirWhite
            Product methadone40ExirWhite = new Product(
                    908,
                    methadone40,
                    methadone40.getName()+"-"+Exir.getName()+"-"+White,
                    Exir,
                    methadone,
                    MedicineType.TABLET
            );
            //methadone40ZagrosdarouWhite
            Product methadone40ZagrosdarouWhite = new Product(
                    909,
                    methadone40,
                    methadone40.getName()+"-"+Zagrosdarou.getName()+"-"+White,
                    Zagrosdarou,
                    methadone,
                    MedicineType.TABLET
            );

            /***
             * Product methadoneSyrup
             */
            //methadoneSyrupDarouPakhsh
            Product methadoneSyrupDarouPakhsh = new Product(
                    910,
                    methadoneSyrup,
                    methadoneSyrup.getName()+"-"+DarouPakhsh.getName(),
                    DarouPakhsh,
                    methadone,
                    MedicineType.SYRUP);
            //methadoneSyrupExir
            Product methadoneSyrupExir = new Product(
                    911,
                    methadoneSyrup,
                    methadoneSyrup.getName()+"-"+Exir.getName(),
                    Exir,
                    methadone,
                    MedicineType.SYRUP);

            /***
             * Product opiumSyrup
             */
            //opiumDarouPakhsh
            Product opiumDarouPakhsh = new Product(
                    912,
                    opiumSyrup,
                    opiumSyrup.getName()+"-"+DarouPakhsh.getName(),
                    DarouPakhsh,
                    opium,
                    MedicineType.SYRUP);

            //opiumTemad
            Product opiumTemad = new Product(
                    914,
                    opiumSyrup,
                    opiumSyrup.getName()+"-"+Temad.getName(),
                    Temad,
                    opium,
                    MedicineType.SYRUP);

            //opiumFaranShimi
            Product opiumFaranShimi = new Product(
                    915,
                    opiumSyrup,
                    opiumSyrup.getName()+"-"+FaranShimi.getName(),
                    FaranShimi,
                    opium,
                    MedicineType.SYRUP
            );
            /***
             * Product buprenorphine
             */
            //buprenorphineFaran
            Product buprenorphineFaran= new Product(
                    916,
                    buprenorphineTablet,
                    buprenorphineTablet.getName()+"-"+Faran.getName(),
                    Faran,
                    buprenorphine,
                    MedicineType.TABLET
            );
            //buprenorphineExir
            Product buprenorphineExir = new Product(
                    917,
                    buprenorphineTablet,
                    buprenorphineTablet.getName()+"-"+Exir.getName(),
                    Exir,
                    buprenorphine,MedicineType.TABLET
            );
            //buprenorphineTemad
            Product buprenorphineTemad= new Product(
                    918,
                    buprenorphineTablet,
                    buprenorphineTablet.getName()+"-"+Temad.getName(),
                    Temad,
                    buprenorphine,
                    MedicineType.TABLET
            );
            //buprenorphineDarouDarmanPars
            Product buprenorphineDarouDarmanPars = new Product(
                    919,
                    buprenorphineTablet,
                    buprenorphineTablet.getName()+"-"+DarouDarmanPars.getName(),
                    DarouDarmanPars,
                    buprenorphine,
                    MedicineType.TABLET
            );

            Product buprenorphineMehrDarou  = new Product(
                    920,
                    buprenorphineTablet,
                    buprenorphineTablet.getName()+"-"+MehrDarou.getName(),
                    MehrDarou,
                    buprenorphine,
                    MedicineType.TABLET
            );
            /***
             * Product suboxone
             */

            Product suboxoneDarouDarmanPars= new Product(
                    921,
                    suboxoneTablet,
                    suboxoneTablet.getName()+"-"+DarouDarmanPars.getName(),
                    DarouDarmanPars,
                    suboxone,
                    MedicineType.TABLET
            );

            //Save
            //methadone5
            repository.save(methadone5DarouPakhsh);
            repository.save(methadone5Zagrosdarou);

            repository.save(methadone20DarouPakhshWhite);
            repository.save(methadone20MehrDarouBrown);
            repository.save(methadone20BrownDarouDarmanPars);
            repository.save(methadone20ZagrosdarouWhite);


            repository.save(methadone40DarouPakhshWhite);
            repository.save(methadone40MehrDarouBrown);
            repository.save(methadone40ExirWhite);
            repository.save(methadone40ZagrosdarouWhite);

            repository.save(methadoneSyrupDarouPakhsh);
            repository.save(methadoneSyrupExir);

            repository.save(opiumDarouPakhsh);
            repository.save(opiumTemad);
            repository.save(opiumFaranShimi);

            repository.save(buprenorphineFaran);
            repository.save(buprenorphineExir);
            repository.save(buprenorphineTemad);
            repository.save(buprenorphineDarouDarmanPars);
            repository.save(buprenorphineMehrDarou);

            repository.save(suboxoneDarouDarmanPars);

            //convert
            //methadone5
            //added ProductUnitsConversion
            methadone5DarouPakhsh.getUnitsConversion().add(
                            new ProductUnitsConversion(methadone5DarouPakhsh,UnitsMeasurement.MILLIGRAM,5.0, MathematicalSign.MULTIPLICATION)
                    );

            //
            methadone5Zagrosdarou.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone5Zagrosdarou,UnitsMeasurement.MILLIGRAM,5.0, MathematicalSign.MULTIPLICATION)
            );


            //methadone20
            //methadone20DarouPakhshWhite
            methadone20DarouPakhshWhite.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone20DarouPakhshWhite,UnitsMeasurement.MILLIGRAM,20.0, MathematicalSign.MULTIPLICATION)
            );
            //methadone20MehrDarouBrown
            methadone20MehrDarouBrown.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone20MehrDarouBrown,UnitsMeasurement.MILLIGRAM,20.0, MathematicalSign.MULTIPLICATION)
            );
            //methadone20BrownDarouDarmanPars
            methadone20BrownDarouDarmanPars.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone20BrownDarouDarmanPars,UnitsMeasurement.MILLIGRAM,20.0, MathematicalSign.MULTIPLICATION)
            );
            //methadone20ZagrosdarouWhite
            methadone20ZagrosdarouWhite.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone20ZagrosdarouWhite,UnitsMeasurement.MILLIGRAM,20.0, MathematicalSign.MULTIPLICATION)
            );

            //methadone40
            methadone40DarouPakhshWhite.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone40DarouPakhshWhite,UnitsMeasurement.MILLIGRAM,40.0, MathematicalSign.MULTIPLICATION)
            );
            methadone40MehrDarouBrown.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone40MehrDarouBrown,UnitsMeasurement.MILLIGRAM,40.0, MathematicalSign.MULTIPLICATION)
            );
            methadone40ExirWhite.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone40ExirWhite,UnitsMeasurement.MILLIGRAM,40.0, MathematicalSign.MULTIPLICATION)
            );
            methadone40ZagrosdarouWhite.getUnitsConversion().add(
                    new ProductUnitsConversion(methadone40ZagrosdarouWhite,UnitsMeasurement.MILLIGRAM,40.0, MathematicalSign.MULTIPLICATION)
            );


            //methadoneSyrup
            //methadoneSyrupDarouPakhsh
            List<ProductUnitsConversion> methadoneSyrupDarouPakhshUnits = new ArrayList<>();
            methadoneSyrupDarouPakhshUnits.add(
                    new ProductUnitsConversion(methadoneSyrupDarouPakhsh, UnitsMeasurement.CUBIC_CENTIMETRE,250.00, MathematicalSign.MULTIPLICATION)
            );
            methadoneSyrupDarouPakhshUnits.add(
                    new ProductUnitsConversion(methadoneSyrupDarouPakhsh,UnitsMeasurement.MILLIGRAM,1250.00, MathematicalSign.MULTIPLICATION)
            );
            methadoneSyrupDarouPakhsh.getUnitsConversion().addAll(methadoneSyrupDarouPakhshUnits);

            //SyrupExirUnits
            List<ProductUnitsConversion> methadoneSyrupExirUnits = new ArrayList<>();
            methadoneSyrupExirUnits.add(
                    new ProductUnitsConversion(methadoneSyrupExir,UnitsMeasurement.CUBIC_CENTIMETRE,250.00, MathematicalSign.MULTIPLICATION)
            );
            methadoneSyrupExirUnits.add(
                    new ProductUnitsConversion(methadoneSyrupExir,UnitsMeasurement.MILLIGRAM,1250.00, MathematicalSign.MULTIPLICATION)
            );

            methadoneSyrupExir.getUnitsConversion().addAll(methadoneSyrupExirUnits);


            //opiumP
            opiumDarouPakhsh.getUnitsConversion().add(
                    new ProductUnitsConversion(opiumDarouPakhsh,UnitsMeasurement.CUBIC_CENTIMETRE,250.0, MathematicalSign.MULTIPLICATION)
            );
            opiumTemad.getUnitsConversion().add(
                    new ProductUnitsConversion(opiumTemad,UnitsMeasurement.CUBIC_CENTIMETRE,250.0, MathematicalSign.MULTIPLICATION)
            );
            opiumFaranShimi.getUnitsConversion().add(
                    new ProductUnitsConversion(opiumFaranShimi,UnitsMeasurement.CUBIC_CENTIMETRE,250.0, MathematicalSign.MULTIPLICATION)
            );



            //buprenorphineP
            buprenorphineFaran.getUnitsConversion().add(
                    new ProductUnitsConversion(buprenorphineFaran,UnitsMeasurement.MILLIGRAM,2.0, MathematicalSign.MULTIPLICATION)
            );
            buprenorphineExir.getUnitsConversion().add(
                    new ProductUnitsConversion(buprenorphineExir,UnitsMeasurement.MILLIGRAM,2.0, MathematicalSign.MULTIPLICATION)
            );
            buprenorphineTemad.getUnitsConversion().add(
                    new ProductUnitsConversion(buprenorphineTemad,UnitsMeasurement.MILLIGRAM,2.0, MathematicalSign.MULTIPLICATION)
            );
            buprenorphineDarouDarmanPars.getUnitsConversion().add(
                    new ProductUnitsConversion(buprenorphineDarouDarmanPars,UnitsMeasurement.MILLIGRAM,2.0, MathematicalSign.MULTIPLICATION)
            );
            buprenorphineMehrDarou.getUnitsConversion().add(
                    new ProductUnitsConversion(buprenorphineMehrDarou,UnitsMeasurement.MILLIGRAM,2.0, MathematicalSign.MULTIPLICATION)
            );



            //suboxoneP
            suboxoneDarouDarmanPars.getUnitsConversion().add(
                    new ProductUnitsConversion(suboxoneDarouDarmanPars,UnitsMeasurement.MILLIGRAM,2.0, MathematicalSign.MULTIPLICATION)
            );

            //methadone5
            repository.save(methadone5DarouPakhsh);
            repository.save(methadone5Zagrosdarou);

            //methadone20
            repository.save(methadone20DarouPakhshWhite);
            repository.save(methadone20MehrDarouBrown);
            repository.save(methadone20BrownDarouDarmanPars);
            repository.save(methadone20ZagrosdarouWhite);

            //methadone40
            repository.save(methadone40DarouPakhshWhite);
            repository.save(methadone40MehrDarouBrown);
            repository.save(methadone40ExirWhite);
            repository.save(methadone40ZagrosdarouWhite);

            //methadoneSyrup
            repository.save(methadoneSyrupDarouPakhsh);
            repository.save(methadoneSyrupExir);

            //opium
            repository.save(opiumDarouPakhsh);
            repository.save(opiumTemad);
            repository.save(opiumFaranShimi);

            //buprenorphine
            repository.save(buprenorphineFaran);
            repository.save(buprenorphineExir);
            repository.save(buprenorphineTemad);
            repository.save(buprenorphineDarouDarmanPars);
            repository.save(buprenorphineMehrDarou);

            //suboxone
            repository.save(suboxoneDarouDarmanPars);
        }
    }

    @Override
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public ResponseEntity<List<Map<String, Object>>>itemsMap() {


        try {
            List<Map<String, Object>> mm =repository.getProductItemsInventoryMap();
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mm.forEach(ll->{
                System.out.println(ll.isEmpty());
                System.out.println(ll.get("quantity"));
            });

            return ResponseEntity.ok().body(mm);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }

    }

}
