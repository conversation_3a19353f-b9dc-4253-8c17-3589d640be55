package keleshteri.clinic.management.service;


import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.pharmacy.product.payload.ProductItemInventory;
import org.springframework.http.ResponseEntity;
import java.util.List;
import java.util.Map;


public interface ProductService {

    /**
     * find by id
     */
    Product find(Long id);

    /**
     * find by name
     */
    Product find(String name);

    /**
     * all Products
     */
    List<Product> all();

    /**
     * all ProductItem
     */
    List<Product>productItemsByPeriod(InventoryPeriod period);

    /**
     * all product in items with Inventory
     */
     List<ProductItemInventory> items();

    /**
     *
     * @param period
     * @return
     */
    List<ProductItemInventory> productItemsInventoryByPeriod(InventoryPeriod period);

    /**
     *
     * @param period
     * @return
     */
    List<ProductItemInventory> productItemsInventoryVirtualByPeriod(InventoryPeriod period);
    /**
     * ResponseEntity Products  pagination
     */
    ResponseEntity<List<Product>> pagination();

    /**
     * Create a New Product
     */
    Product create(Product product);

    /**
     * Update a Product
     */
    Product update(Long id, Product productDetails);

    /**
     * Delete a Product
     */
    Map<String, Boolean> delete(Long id);

    /**
     * seeder
     */
    void seeder();

    ResponseEntity<List<Map<String, Object>>> itemsMap();


}
