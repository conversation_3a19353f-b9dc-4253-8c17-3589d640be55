package keleshteri.clinic.management.global;

import org.springframework.data.domain.Sort;


public class MainController {

    // String word asc Or Desc to
    protected Sort.Direction getSortDirection(String direction) {
        if (direction.equals("asc")) {
            return Sort.Direction.ASC;
        } else if (direction.equals("desc")) {
            return Sort.Direction.DESC;
        }

        return Sort.Direction.ASC;
    }
}
