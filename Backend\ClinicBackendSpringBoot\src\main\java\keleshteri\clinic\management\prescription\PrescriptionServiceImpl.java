package keleshteri.clinic.management.prescription;

import com.github.mfathi91.time.PersianDate;
import com.google.common.math.IntMath;
import keleshteri.clinic.management.exception.*;
import keleshteri.clinic.management.patient.helper.PrescriptionExportExcelHelper;
import keleshteri.clinic.management.patient.helper.PrescriptionPeriodExportExcelHelper;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.patient.virtual.PrescriptionPeriod;
import keleshteri.clinic.management.pharmacy.delivery.model.Delivery;
import keleshteri.clinic.management.pharmacy.delivery.model.DeliveryDetails;
import keleshteri.clinic.management.pharmacy.delivery.repository.DeliveryRepository;
import keleshteri.clinic.management.pharmacy.delivery.service.DeliveryDetailsService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryPeriodService;
import keleshteri.clinic.management.pharmacy.delivery.service.DeliveryService;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.prescription.payload.PrescriptionPaginationResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class PrescriptionServiceImpl implements PrescriptionService {

    //Repository
    @Autowired
    private  PrescriptionRepository repository;

    @Autowired
    private  DeliveryRepository deliveryRepository;

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private  DeliveryDetailsService deliveryDetailsService;

    @Autowired
    private InventoryPeriodService inventoryPeriodService;



    /***
     * Prescription all
     ***/
    public List<Prescription> all() {
//        return repository.findAll();
        return repository.findByOrderByCreatedAtAsc();
    }

    /***
     * find all Prescription by Patient
     ***/
    public List<Prescription> all(Patient patient){
        return repository.findByPatient(patient);
    }

    @Override
    public List<Prescription> allToday() {
//        Date today = new Date();
        LocalDate today = LocalDate.now();

        return repository.getPrescriptionsByCreatedAt(today);
    }

    /***
     * prescription
     ***/
    public Prescription findFirst(Long id){
        Prescription prescription = repository.findById(id)
                .orElseThrow(()-> new ResourceNotFoundException("prescription not exist with id :" + id));
        return  prescription;
    }

    /***
     * find
     ***/
    public Prescription find(Long id) {
        Prescription prescription = this.findFirst(id);
        return prescription;
    }

    /***
     * pagination
     ***/
    public ResponseEntity<PrescriptionPaginationResponse> pagination(int page, int size, String sortBy, String sortDir,
            String fileNumber, String startDate, String medicineId) {
        log.debug("Pagination request - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir);
        log.debug("Filter parameters - fileNumber: {}, startDate: {}, medicineId: {}", fileNumber, startDate, medicineId);
        
        Sort sort = sortDir.equalsIgnoreCase(Sort.Direction.ASC.name()) ? Sort.by(sortBy).ascending()
                : Sort.by(sortBy).descending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        
        // Create Specification for filtering
        Specification<Prescription> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Filter by patient file number
            if (fileNumber != null && !fileNumber.isEmpty()) {
                log.debug("Applying file number filter: {}", fileNumber);
                Join<Prescription, Patient> patientJoin = root.join("patient");
                predicates.add(criteriaBuilder.equal(patientJoin.get("fileNumber"), fileNumber));
            }
            
            // Filter by start date
            if (startDate != null && !startDate.isEmpty()) {
                log.debug("Applying start date filter: {}", startDate);
                predicates.add(criteriaBuilder.equal(root.get("startDatePersian"), startDate));
            }
            
            // Filter by medicine
            if (medicineId != null && !medicineId.isEmpty()) {
                log.debug("Applying medicine filter: {}", medicineId);
                System.out.println("medicineId: "+medicineId);
                Join<Prescription, Medicine> medicineJoin = root.join("medicine");
                predicates.add(criteriaBuilder.equal(medicineJoin.get("id"), Long.valueOf(medicineId)));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<Prescription> prescriptionsPage = repository.findAll(spec, pageable);
        log.debug("Found {} prescriptions matching criteria", prescriptionsPage.getTotalElements());
        
        List<Prescription> prescriptions = prescriptionsPage.getContent();
        
        PrescriptionPaginationResponse paginationResponse = new PrescriptionPaginationResponse();
        paginationResponse.setContent(prescriptions);
        paginationResponse.setPageNo(prescriptionsPage.getNumber());
        paginationResponse.setPageSize(prescriptionsPage.getSize());
        paginationResponse.setTotalElements(prescriptionsPage.getTotalElements());
        paginationResponse.setTotalPages(prescriptionsPage.getTotalPages());
        paginationResponse.setLast(prescriptionsPage.isLast());
        paginationResponse.setFirst(prescriptionsPage.isFirst());

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", String.format("prescriptions %d-%d/%d", 
            page * size, 
            Math.min((page + 1) * size - 1, prescriptionsPage.getTotalElements()),
            prescriptionsPage.getTotalElements()));
        responseHeaders.set("X-Total-Count", String.valueOf(prescriptionsPage.getTotalElements()));
        responseHeaders.set("X-Total-Pages", String.valueOf(prescriptionsPage.getTotalPages()));

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(paginationResponse);
    }

    /**
     * ExpiredToDayList
     *
     */
    public ResponseEntity<List<Prescription>> ExpiredToDayListPagination() {
        LocalDate today = LocalDate.now();
        //get all for today
//        List<Prescription> prescriptions = repository.findByEndDate(today);
        List<Prescription> prescriptions = repository.PrescriptionByExpiryDateDeleted(today,false);

        System.out.println(prescriptions.size());

        //check If has prescriptions more than this date
        List<Prescription> prescriptionsOut = prescriptions.stream().filter(p->{

            if(this.repository.existsPrescriptionByEndDateGreaterThanAndPatient( today,p.getPatient()))
                return false;
            else
                return true;

          }).collect(Collectors.toList());



        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "prescriptions 0-"+prescriptionsOut.size() + "/" + prescriptionsOut.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(prescriptionsOut);
    }

    /**
     *
     */
    public ResponseEntity<List<Prescription>> paginationToday(){
        List<Prescription> prescriptions = this.allToday();

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "prescriptions 0-"+prescriptions.size() + "/" + prescriptions.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(prescriptions);

    }

    /***
     * paginationPrescriptionPeriodTodayDelivery
     ***/
    public ResponseEntity<List<PrescriptionPeriod>> paginationPrescriptionPeriodTodayDelivery() {

        LocalDate today = LocalDate.now();
        PersianDate persianDateToday= PersianDate.now();
        System.out.println("here code");

        List<Prescription> prescriptions = this.repository.findByEndDateGreaterThanEqual(today);


        List<PrescriptionPeriod> prescriptionsPeriod= new ArrayList<>();

        prescriptions.forEach(prt->{


            //startDate
            PersianDate startDatePersian = PersianDate.parse(prt.getStartDatePersian());
            //endDate
            PersianDate endDatePersian   = PersianDate.parse(prt.getEndDatePersian());
            //
            Integer deliveryPeriodDays = prt.getDeliveryPeriodDay()==0 ? 14:prt.getDeliveryPeriodDay();

            Integer PeriodDaysTime = IntMath.divide(prt.getTreatmentPeriodDay(), deliveryPeriodDays, RoundingMode.CEILING);

            //sum dose daily  in Period
            Double AllDose = prt.getDosePeriod();

            //create MedicineDeliveryService
            //make time of delivery

            Integer AddPeroiedDate=0;
            Integer PeriodDaysLeft = prt.getTreatmentPeriodDay();

            Double DailyDose =prt.getDailyDose();
            LocalDate datedelivery =prt.getStartDate();

            LocalDate dateDailyDelivery =prt.getStartDate();
            int count=0;
            //Loop
            for(int i =0;i<PeriodDaysTime;i++) {
                count++;
                //create date
                PersianDate persianDate = prt.getStartDateInPersianDate().plusDays(AddPeroiedDate);

                Double PeroidDose=0.0;
                //create dose
                if(PeriodDaysLeft < deliveryPeriodDays){
                    PeroidDose = DailyDose* PeriodDaysLeft;
                }else {
                    PeroidDose = DailyDose* deliveryPeriodDays;
                }
                //add day to date
                AddPeroiedDate=AddPeroiedDate+deliveryPeriodDays;
                //Dose left
                AllDose= AllDose-PeroidDose;

                System.out.println("persianDate:"+persianDate);
                System.out.println("miladi:"+persianDate.toGregorian());
                System.out.println("taday:"+today);
                System.out.println("persianDateToday:"+persianDateToday);
                System.out.println("------------------");

                if(persianDate.toGregorian().isEqual(today)==true){
                    System.out.println("Date:"+persianDate.toString());
                }
                //create and save
                prescriptionsPeriod.add(new PrescriptionPeriod(
                        prt,//prescription
                        persianDate.toString(),//startDatePersian
                        persianDate.toGregorian(),//startDate
                        prt.getExpired(),//expired
                        prt.getMedicine(),//medicine
                        prt.getDoseUnits(),//doseUnits
                        PeroidDose,//dosePeriod
                        AllDose,//doseLeft
                        (PeriodDaysLeft > deliveryPeriodDays) ? deliveryPeriodDays:PeriodDaysLeft ,//periodDays
                        deliveryPeriodDays
                ));

                //remove days form all days
                PeriodDaysLeft =PeriodDaysLeft-deliveryPeriodDays;
            }
        });

        //filtered
        List<PrescriptionPeriod> prescriptionsPeriodFiltered= prescriptionsPeriod.stream().filter(p->{
            System.out.println("T::"+p.getStartDate().toString());
            return p.getStartDate().isEqual(today);
        }).collect(Collectors.toList());
        System.out.println(prescriptionsPeriodFiltered.size());





        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "prescriptions 0-"+prescriptionsPeriodFiltered.size() + "/" + prescriptionsPeriodFiltered.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(prescriptionsPeriodFiltered);
    }

    /***
     * pagination
     ***/
    public ResponseEntity<List<PrescriptionPeriod>> paginationPrescriptionPeriodToday() {

        LocalDate today = LocalDate.now();
        PersianDate persianDateToday= PersianDate.now();
        System.out.println(today);


        List<Prescription> prescriptions = this.repository.findByStartDate(today);
        System.out.println(prescriptions);


        List<PrescriptionPeriod> prescriptionsPeriod= new ArrayList<>();

        prescriptions.forEach(prt->{


            //startDate
            PersianDate startDatePersian = PersianDate.parse(prt.getStartDatePersian());
            //endDate
            PersianDate endDatePersian   = PersianDate.parse(prt.getEndDatePersian());
            //
            Integer deliveryPeriodDays = prt.getDeliveryPeriodDay()==0 ? 14:prt.getDeliveryPeriodDay();

            Integer PeriodDaysTime = IntMath.divide(prt.getTreatmentPeriodDay(), deliveryPeriodDays, RoundingMode.CEILING);

            //sum dose daily  in Period
            Double AllDose = prt.getDosePeriod();

            //create MedicineDeliveryService
            //make time of delivery

            Integer AddPeroiedDate=0;
            Integer PeriodDaysLeft = prt.getTreatmentPeriodDay();

            Double DailyDose =prt.getDailyDose();
            LocalDate datedelivery =prt.getStartDate();

            LocalDate dateDailyDelivery =prt.getStartDate();
            int count=0;
            //Loop
            for(int i =0;i<PeriodDaysTime;i++) {
                count++;
                //create date
                PersianDate persianDate = prt.getStartDateInPersianDate().plusDays(AddPeroiedDate);

                Double PeroidDose=0.0;
                //create dose
                if(PeriodDaysLeft < deliveryPeriodDays){
                    PeroidDose = DailyDose* PeriodDaysLeft;
                }else {
                    PeroidDose = DailyDose* deliveryPeriodDays;
                }
                //add day to date
                AddPeroiedDate=AddPeroiedDate+deliveryPeriodDays;
                //Dose left
                AllDose= AllDose-PeroidDose;

                System.out.println("persianDate:"+persianDate);
                System.out.println("miladi:"+persianDate.toGregorian());
                System.out.println("taday:"+today);
                System.out.println("persianDateToday:"+persianDateToday);
                System.out.println("------------------");

                if(persianDate.toGregorian().isEqual(today)==true){
                    System.out.println("Date:"+persianDate.toString());
                }
                //create and save
                prescriptionsPeriod.add(new PrescriptionPeriod(
                        prt,//prescription
                        persianDate.toString(),//startDatePersian
                        persianDate.toGregorian(),//startDate
                        prt.getExpired(),//expired
                        prt.getMedicine(),//medicine
                        prt.getDoseUnits(),//doseUnits
                        PeroidDose,//dosePeriod
                        AllDose,//doseLeft
                        (PeriodDaysLeft > deliveryPeriodDays) ? deliveryPeriodDays:PeriodDaysLeft ,//periodDays
                        deliveryPeriodDays
                ));

                //remove days form all days
                PeriodDaysLeft =PeriodDaysLeft-deliveryPeriodDays;
            }
        });

        //filtered
        List<PrescriptionPeriod> prescriptionsPeriodFiltered= prescriptionsPeriod.stream().filter(p->{
            System.out.println("T::"+p.getStartDate().toString());
            return p.getStartDate().isEqual(today);
        }).collect(Collectors.toList());
        System.out.println(prescriptionsPeriodFiltered.size());





        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "prescriptions 0-"+prescriptionsPeriodFiltered.size() + "/" + prescriptionsPeriodFiltered.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(prescriptionsPeriodFiltered);
    }
    /***
     * pagination Prescription by Patient
     */
    public ResponseEntity<List<Prescription>> paginationByPatient(Patient patient) {
        List<Prescription> prescriptions = repository.findByPatient(patient);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "prescriptions 0-"+prescriptions.size() + "/" + prescriptions.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(prescriptions);
    }
    /***
     * create prescription with Validation
     * @param prescription
     *
     */
    @Transactional(rollbackFor = {Exception.class,InvalidPrescriptionException.class})
    public Prescription createWithValidation(Prescription prescription) throws InvalidPrescriptionException {
        /**
         * get Dates and persian and english-  convert to english date
         * Today Date
         */
          //startDate
        PersianDate startDatePersian = PersianDate.parse(prescription.getStartDatePersian());
          //endDate
        PersianDate endDatePersian = PersianDate.parse(prescription.getEndDatePersian());
          //today
        PersianDate todayPersian= PersianDate.now();
        /**
         * check Validate Date
         */
          //if we has limit for TreatmentPeriodDay
//           if(prescription.getTreatmentPeriodDay()<7){
//               throw  new InvalidDataException("اجازه تجویز کمتر از 7 روز نمی باشد");
//           }
          //if we has limit for date of Expiry not be smaller than today date
//          if(today.isAfter(endDatePersian)){
//              throw  new InvalidDateException("تاریخ اتمام: "+endDatePersian  +"کوچکتر از تاریخ امروز هست: "+todayPersian);
//          }
        //check date start not more than expiry
        if(startDatePersian.isAfter(endDatePersian)){
            throw  new InvalidDateException("تاریخ تجویز: "+startDatePersian+" بزرگ تر از تاریخ اتمام هست: "+endDatePersian);
        }
        //check date start same expiry
        if(startDatePersian.isEqual(endDatePersian)){
            throw  new InvalidDateException("تاریخ تجویز: "+startDatePersian+" با تاریخ اتمام یکسان است: "+endDatePersian);
        }
        //check different start and expiry date = treatment PeriodDay
        long periodDays = ChronoUnit.DAYS.between(startDatePersian, endDatePersian);
        if(periodDays != prescription.getTreatmentPeriodDay()){
            throw  new InvalidDateException("تفاوت تاریخ تجویز: "+startDatePersian+" با تاریخ اتمام: "+endDatePersian+"می شود" +"="+periodDays+"اما شما مقدار نامعتبر وارد کردید: "+prescription.getTreatmentPeriodDay());
        }
        //check if date  in active inventory period if not exists
//        if(inventoryPeriodService.existsPeriodByDates(prescription.getStartDate(),prescription.getEndDate(),false)==false){
//            throw new InvalidDateException("این تاریخ تجویز در این دوره فعال خرید نیست");
//        }
        //check date of startDatePersian
        /**
         * medicine Validate
         */
          //medicine unit check
        if(!prescription.getMedicine().getUnits().contains(prescription.getDoseUnits())){
            throw new InvalidDataException("دارو انتخاب شده با واحد اندازه گیری مطابقت ندارد");
        }



        //save
        return repository.save(prescription);
    }

    @Override
    public List<PrescriptionPeriod> allPeriod() {
        List<Prescription> prescriptions = repository.findAll();

        List<PrescriptionPeriod> prescriptionsPeriod= new ArrayList<>();

        prescriptions.forEach(prt->{


            //startDate
            PersianDate startDatePersian = PersianDate.parse(prt.getStartDatePersian());
            //endDate
            PersianDate endDatePersian   = PersianDate.parse(prt.getEndDatePersian());
            //
            Integer deliveryPeriodDays = prt.getDeliveryPeriodDay()==0 ? 14:prt.getDeliveryPeriodDay();

            Integer PeriodDaysTime = IntMath.divide(prt.getTreatmentPeriodDay(), deliveryPeriodDays, RoundingMode.CEILING);

            //sum dose daily  in Period
            Double AllDose = prt.getDosePeriod();

            //create MedicineDeliveryService
            //make time of delivery

            Integer AddPeroiedDate=0;
            Integer PeriodDaysLeft = prt.getTreatmentPeriodDay();

            Double DailyDose =prt.getDailyDose();
            LocalDate datedelivery =prt.getStartDate();

            LocalDate dateDailyDelivery =prt.getStartDate();
            int count=0;
            //Loop
            for(int i =0;i<PeriodDaysTime;i++) {
                count++;
                //create date
                PersianDate persianDate = prt.getStartDateInPersianDate().plusDays(AddPeroiedDate);

                Double PeroidDose=0.0;
                //create dose
                if(PeriodDaysLeft < deliveryPeriodDays){
                    PeroidDose = DailyDose* PeriodDaysLeft;
                }else {
                    PeroidDose = DailyDose* deliveryPeriodDays;
                }
                //add day to date
                AddPeroiedDate=AddPeroiedDate+deliveryPeriodDays;
                //Dose left
                AllDose= AllDose-PeroidDose;
                //create and save
                prescriptionsPeriod.add(new PrescriptionPeriod(
                         prt,//prescription
                         persianDate.toString(),//startDatePersian
                         persianDate.toGregorian(),//startDate
                         prt.getExpired(),//expired
                         prt.getMedicine(),//medicine
                         prt.getDoseUnits(),//doseUnits
                         PeroidDose,//dosePeriod
                         AllDose,//doseLeft
                         (PeriodDaysLeft > deliveryPeriodDays) ? deliveryPeriodDays:PeriodDaysLeft //periodDays
                ));

                //remove days form all days
                PeriodDaysLeft =PeriodDaysLeft-deliveryPeriodDays;
            }
        });
        // return
        return prescriptionsPeriod;
    }

    /**
     *
     * @param prescription
     * @return
     * @throws InvalidPrescriptionException
     */
    @Transactional(rollbackFor = {Exception.class,InvalidPrescriptionException.class})
    public Prescription saveNewPrescription(Prescription prescription) throws InvalidPrescriptionException {
        //check Patient has prescription in same date


        System.out.println(repository.existsPrescriptionByStartDateLessThanEqualAndEndDateGreaterThanEqualAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()));
        System.out.println(repository.existsPrescriptionByStartDateLessThanEqualAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()));
        System.out.println(repository.existsPrescriptionByStartDateLessThanAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()));


        if(repository.existsPrescriptionByStartDateLessThanAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient())){
            Prescription hasOtherPrescription =repository.findByStartDateLessThanAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()).orElseGet(null);


            throw   new RecordExistsException("این بیمار نسخه ای دارد" +
                    " در تاریخ تجویز : "+
                    " شمسی: "+
                    "\n"+
                    hasOtherPrescription.getStartDatePersian()  +" میلادی : " + hasOtherPrescription.getStartDate()
                    +" تاریخ اتمام: "+ " شمسی: " +hasOtherPrescription.getEndDatePersian()  +" میلادی : " + hasOtherPrescription.getEndDate()

                    +"\n"+ "تاریخ درخواستی شما "+"\n"+
                    "  تاریخ تجویز : "+
                    " شمسی: "+prescription.getStartDatePersian() +" میلادی : " + prescription.getStartDate()
                    +"تاریخ اتمام: "+ " شمسی: " +prescription.getEndDatePersian() +" میلادی : " + prescription.getEndDate()

            );
        }
        //check Patient has prescription not yet expired
//        if(repository.existsNotExpiredByPatientId(prescription.getPatient().getId())==true){
//            throw  new RecordExistsException("این بیمار نسخه ای دارد که هنوز منقضی نشده است");
//        }
        //save Prescription
        Prescription savePrescription= this.createWithValidation(prescription);

        return savePrescription;
    }

    @Override
    public ByteArrayInputStream excelFileGenerate() {
        List<Prescription> prescriptions = repository.findAll();

        ByteArrayInputStream in = PrescriptionExportExcelHelper.prescriptionsToExcel(prescriptions);
        return in;
    }

    /**
     *
     * @return
     */
    public ByteArrayInputStream excelPeriodsFileGenerate() {
        List<PrescriptionPeriod> prescriptions = this.allPeriod();

        ByteArrayInputStream in = PrescriptionPeriodExportExcelHelper.prescriptionsToExcel(prescriptions);
        return in;
    }

    @Override
    public ResponseEntity<Prescription> LastPrescriptionByPatient(Patient patient) {
//        Prescription  prescription = repository.findByPatient(patient);
        Prescription prescription= repository.findTopByPatientOrderByEndDateDesc(patient)
                .orElseThrow(
                ()->new ResourceNotFoundException("این بیمار هیچ نسخه ای ندارد.")
        );

        return ResponseEntity.ok().body(prescription);
    }

    /**
     *
     * @param prescription
     * @return
     * @throws InvalidPrescriptionException
     */
    @Transactional(rollbackFor = {Exception.class,InvalidPrescriptionException.class})
    public Prescription savePrescriptionWithDelivery(Prescription prescription) throws InvalidPrescriptionException {
        //check Patient has prescription in same date


        System.out.println(repository.existsPrescriptionByStartDateLessThanEqualAndEndDateGreaterThanEqualAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()));
        System.out.println(repository.existsPrescriptionByStartDateLessThanEqualAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()));
        System.out.println(repository.existsPrescriptionByStartDateLessThanAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()));


        if(repository.existsPrescriptionByStartDateLessThanAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient())){
            Prescription hasOtherPrescription =repository.findByStartDateLessThanAndEndDateGreaterThanAndPatient(prescription.getEndDate(),prescription.getStartDate(),prescription.getPatient()).orElseGet(null);


            throw   new RecordExistsException("این بیمار نسخه ای دارد" +
                    " در تاریخ تجویز : "+
                     " شمسی: "+
                    "\n"+
                    hasOtherPrescription.getStartDatePersian()  +" میلادی : " + hasOtherPrescription.getStartDate()
                    +" تاریخ اتمام: "+ " شمسی: " +hasOtherPrescription.getEndDatePersian()  +" میلادی : " + hasOtherPrescription.getEndDate()

                    +"\n"+ "تاریخ درخواستی شما "+"\n"+
                    "  تاریخ تجویز : "+
                     " شمسی: "+prescription.getStartDatePersian() +" میلادی : " + prescription.getStartDate()
                     +"تاریخ اتمام: "+ " شمسی: " +prescription.getEndDatePersian() +" میلادی : " + prescription.getEndDate()

            );
        }
        //check Patient has prescription not yet expired
//        if(repository.existsNotExpiredByPatientId(prescription.getPatient().getId())==true){
//            throw  new RecordExistsException("این بیمار نسخه ای دارد که هنوز منقضی نشده است");
//        }
        //save Prescription
        Prescription savePrescription= this.createWithValidation(prescription);

        //create delivery
        this.createDelivery(savePrescription);
        //
        return savePrescription;
    }

    /**
     *
     * @param prescription
     * @return
     * @throws InvalidPrescriptionException
     */
    public void createDelivery(Prescription prescription) throws InvalidPrescriptionException {

        //startDate
        PersianDate startDatePersian = PersianDate.parse(prescription.getStartDatePersian());
        //endDate
        PersianDate endDatePersian = PersianDate.parse(prescription.getEndDatePersian());

        //get
//        Integer treatmentDays     = prescription.getTreatmentPeriodDay();
        //period day for delivery each
        Integer deliveryPeriodDays = prescription.getDeliveryPeriodDay()==0 ? 14:prescription.getDeliveryPeriodDay();

        Integer PeriodDaysTime = IntMath.divide(prescription.getTreatmentPeriodDay(), deliveryPeriodDays, RoundingMode.CEILING);

        //sum dose daily  in Period
        Double AllDose = prescription.getDosePeriod();

        //create MedicineDeliveryService
        //make time of delivery

        Integer AddPeroiedDate=0;
        Integer PeriodDaysLeft = prescription.getTreatmentPeriodDay();

        Double DailyDose =prescription.getDailyDose();
        LocalDate datedelivery =prescription.getStartDate();

        LocalDate dateDailyDelivery =prescription.getStartDate();


        //Loop
        for(int i =0;i<PeriodDaysTime;i++) {

            //create date
//            datedelivery=prescription.getStartDate().plusDays(AddPeroiedDate);
//            PersianDate persianDate = PersianDate.fromGregorian(datedelivery);
              PersianDate persianDate = prescription.getStartDateInPersianDate().plusDays(AddPeroiedDate);


            Double PeroidDose=0.0;
            //create dose
            if(PeriodDaysLeft < deliveryPeriodDays){
                PeroidDose = DailyDose* PeriodDaysLeft;
            }else {
                PeroidDose = DailyDose* deliveryPeriodDays;
            }
            //add day to date
            AddPeroiedDate=AddPeroiedDate+deliveryPeriodDays;
            //Dose left
            AllDose= AllDose-PeroidDose;
            //create and save
//            Delivery deliverySave= this.createDelivery(prescription,prescription.getMedicine(),persianDate.toString(),datedelivery,PeroidDose,AllDose,(PeriodDaysLeft > deliveryPeriodDays) ? deliveryPeriodDays:PeriodDaysLeft);
            Delivery deliverySave= deliveryService.create(new Delivery(
                    persianDate.toString(),
                    prescription,
                    PeroidDose,
                    AllDose,
                    (PeriodDaysLeft > deliveryPeriodDays) ? deliveryPeriodDays:PeriodDaysLeft
            ));
            // inner loop prints days
            for (int j = 1; j <= ((PeriodDaysLeft > deliveryPeriodDays) ? deliveryPeriodDays:PeriodDaysLeft); ++j) {
                PersianDate persianDailyDate = persianDate.plusDays(j);

                DeliveryDetails deliveryDetails =
                deliveryDetailsService.create(
                        new DeliveryDetails(
                                deliverySave,
                                prescription.getDailyDose(),
                                persianDailyDate.toString(),
                                persianDailyDate.equals(persianDailyDate)? true:false
                        )
                );

            }
            //remove days form all days
            PeriodDaysLeft =PeriodDaysLeft-deliveryPeriodDays;
        }

    }


    /**
     *
     * @param id
     * @param prescriptionDetails
     * @throws InvalidPrescriptionException
     */
    @Transactional(rollbackFor = {Exception.class,InvalidPrescriptionException.class})
    @Override
    public Prescription update(Long id, Prescription prescriptionDetails)  throws InvalidPrescriptionException {

        Prescription prescriptionUpdate = this.findFirst(id);

        if(prescriptionUpdate.getExpired()==true){
            throw  new RecordExistsException("این  نسخه   منقضی  شده است قابل ویرایش نیست");
        }

        //sum dose daily  in Period
        Double AllDose = prescriptionDetails.getDailyDose()*prescriptionDetails.getTreatmentPeriodDay();
        prescriptionDetails.setDosePeriod(AllDose);

        //prescriptionUpdate

        //startDate 2
        prescriptionUpdate.setStartDatePersian(prescriptionDetails.getStartDatePersian());
        //expiryDate 3
        prescriptionUpdate.setEndDatePersian(prescriptionDetails.getEndDatePersian());
        //Medicine 4
        prescriptionUpdate.setMedicine(prescriptionDetails.getMedicine());
        //Units 5
        prescriptionUpdate.setDoseUnits(prescriptionDetails.getDoseUnits());
        //dailyDose 6
        prescriptionUpdate.setDailyDose(prescriptionDetails.getDailyDose());
        //treatmentPeriodDay 7
        prescriptionUpdate.setTreatmentPeriodDay(prescriptionDetails.getTreatmentPeriodDay());

        //save Prescription
        Prescription savePrescription= this.createWithValidation(prescriptionUpdate);

         //if has  MedicineDeliveries
        if(savePrescription.getMedicineDeliveries().size()>0){
            //delete all datedelivery before new save
            deliveryService.deleteByPrescription(prescriptionUpdate);
            //create delivery
            this.createDelivery(savePrescription);
        }

        return savePrescription;
    }


    public Map<String, Boolean> delete(Long id) {
        Prescription prescription = this.findFirst(id);
        //delete
        repository.delete(prescription);
        //response
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return response;
    }

    public Long countPrescription() {
        return repository.count();
    }




}
