package keleshteri.clinic.management.pharmacy.medicine.service;


import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

public interface MedicineService {

    /**
     * find by id
     */
      Medicine find(Long id);

    /**
     * find by name
     */
      Medicine find(String name);

    /**
     * all medicine
     */
      List<Medicine> all();

    /**
     * ResponseEntity medicine pagination
     */
      ResponseEntity<List<Medicine>> pagination();

    /**
     * Create a New Medicine
     */
      Medicine create(Medicine medicine);

    /**
     * Update Medicine
     */
      Medicine update(Long id, Medicine medicineDetails);

    /**
     * Delete
     */
      Map<String, Boolean> delete(Long id);

    /**
     * seeder
     */
      void seeder();


}
