package keleshteri.clinic.management.laboratory.controller;


import keleshteri.clinic.management.laboratory.model.LaboratoryTest;
import keleshteri.clinic.management.laboratory.service.LaboratoryTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/laboratory/tests")
public class LaboratoryTestController {

    private  final LaboratoryTestService laboratoryTestService;

    @Autowired
    public LaboratoryTestController(LaboratoryTestService laboratoryTestService) {

        this.laboratoryTestService = laboratoryTestService;
    }

    /**
     * Get All Tests
     */
    @GetMapping
    public ResponseEntity<List<LaboratoryTest>>getAll(){
        return ResponseEntity.ok(this.laboratoryTestService.all());
    }

    /**
     * Get a Test  BY ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<LaboratoryTest>getById(@PathVariable Long id){
        return ResponseEntity.ok(this.laboratoryTestService.find(id));
    }

    /**
     * Create a Test
     */
    @PostMapping
    public ResponseEntity<LaboratoryTest> create(@Valid @RequestBody LaboratoryTest laboratoryTest){
        return ResponseEntity.ok(this.laboratoryTestService.create(laboratoryTest));
    }

    /**
     * update a Test
     */
    @PutMapping("/{id}")
    public ResponseEntity<LaboratoryTest> update(@PathVariable Long id,@RequestBody LaboratoryTest laboratoryTest){
        return ResponseEntity.ok(this.laboratoryTestService.update(id,laboratoryTest));
    }

    /**
     * Delete a Test
     *
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> delete(@PathVariable Long id){
        return ResponseEntity.ok(this.laboratoryTestService.delete(id));
    }
}
