package keleshteri.clinic.management.pharmacy.virtual;

import keleshteri.clinic.management.enums.InventoryType;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class InventoryInventoryItemBalance {

    private InventoryItem inventoryItem;
    private Double balance;
    private Double quantityAdd;
    private Double quantityRemove;
    private InventoryType inventoryType;

    /**
     * Getter
     */
    public String getInventoryItem() {
        return inventoryItem.getProductName();
    }

    public String getInventoryType() {
        if(inventoryType!=null) {
            return inventoryType.getLabel();
        }
        return null;
    }

    public InventoryInventoryItemBalance(
            InventoryItem inventoryItem,
            Double balance,
            Double quantityAdd,
            Double quantityRemove,
            InventoryType inventoryType
    ) {
        this.inventoryItem = inventoryItem;
        this.balance = balance;
        this.quantityAdd = quantityAdd;
        this.quantityRemove = quantityRemove;
        this.inventoryType = inventoryType;
    }
    public InventoryInventoryItemBalance(
            InventoryItem inventoryItem,
            Double balance,
            Double quantityAdd,
            Double quantityRemove
    ) {
        this.inventoryItem = inventoryItem;
        this.balance = balance;
        this.quantityAdd = quantityAdd;
        this.quantityRemove = quantityRemove;
    }

    /**
     * Constructor
     */

    public InventoryInventoryItemBalance(
            InventoryItem inventoryItem,
            Double balance,
            InventoryType inventoryType
    ) {
        this.inventoryItem = inventoryItem;
        this.balance = balance;
        this.inventoryType = inventoryType;
    }

    public InventoryInventoryItemBalance(InventoryItem inventoryItem, Double balance) {
        this.inventoryItem = inventoryItem;
        this.balance = balance;
    }

    public InventoryInventoryItemBalance(Double balance) {
        this.balance = balance;
    }
}
