package keleshteri.clinic.management.repository;

import keleshteri.clinic.management.enums.CalendarType;
import keleshteri.clinic.management.model.EventsCalendar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventsCalendarRepository extends JpaRepository<EventsCalendar,Long> {

    List<EventsCalendar> findByCalendarTypeAndMonthAndDay(CalendarType type, int month, int day);
}
