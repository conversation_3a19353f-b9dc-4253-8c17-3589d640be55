package keleshteri.clinic.management.pharmacy.inventory.service;

import keleshteri.clinic.management.exception.InventoryException;
import keleshteri.clinic.management.enums.InventoryType;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryOriginal;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.purchase.model.Purchase;
import keleshteri.clinic.management.pharmacy.sale.model.Sale;
import keleshteri.clinic.management.pharmacy.virtual.InventoryMedicineBalance;
import keleshteri.clinic.management.pharmacy.virtual.InventoryInventoryItemBalance;
import keleshteri.clinic.management.pharmacy.virtual.InventoryProductBalance;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface InventoryOriginalService {

    /***
     * find a InventoryOriginal by id
     ***/
    InventoryOriginal find(Long id);

    /**
     * List InventoryOriginal
     */
    List<InventoryOriginal> all();


    /**
     * List InventoryOriginal by Period
     */
    List<InventoryOriginal>all(InventoryPeriod period);


    /**
     * Pagination Original
     */
    ResponseEntity<Page<InventoryOriginal>> Pagination(
    Optional<Integer> size,
    Optional<Integer> page,
    Optional<String> sortBy,
    boolean desc
            );

    /**
     * Pagination Original
     */
    ResponseEntity<Page<InventoryOriginal>> Pagination(
            InventoryPeriod period,
            Optional<Integer> size,
            Optional<Integer> page,
            Optional<String> sortBy,
            boolean desc
    );

    /**
     * create a InventoryOriginal
     */
    InventoryOriginal create(InventoryOriginal inventoryOriginal);

    /**
     * Update a InventoryOriginal
     */
    InventoryOriginal update(Long id, InventoryOriginal inventoryOriginalDetails);



    /**
     * add item
     */
    void addItem(Purchase purchase);

    /**
     * remove item
     */
    void removeItem(Sale sale);

    /**
     * Return item
     */
    void returnItem(Sale sale);



    /**
     * Deleted record
     */
    Map<String, Boolean> delete(Long id);

    /**
     * balance items
     */
     Double stockBalance(InventoryItem inventoryItem);

    /**
     * balance items
     */
    Double stockBalance2(InventoryItem inventoryItem);

    /**
     * existsByInventoryItem
     * return boolean
     */
    boolean existsItem(InventoryItem item);
    /**
     * existsByInventoryItem
     * return boolean
     */
    boolean existsItem(InventoryItem item, InventoryType type);

    /**
     * existsByInventoryItem Exception
     */
    void existsItemException(InventoryItem item) throws InventoryException;

    /**
     *
     * get MedicineInventory
     */
    List<InventoryMedicineBalance> getMedicineInventoryAll();


    List<InventoryMedicineBalance> getMedicineInventoryPeriodAll(InventoryPeriod period);

    List<InventoryInventoryItemBalance> getItemInventoryPeriodAll(InventoryPeriod period);

    List<InventoryInventoryItemBalance> getItemInventoryTypePeriodAll(InventoryPeriod period);

    List<InventoryProductBalance> getProductInventoryPeriodAll(InventoryPeriod period);

    List<InventoryProductBalance> getProductGenericInventoryPeriodAll(InventoryPeriod period);
}

