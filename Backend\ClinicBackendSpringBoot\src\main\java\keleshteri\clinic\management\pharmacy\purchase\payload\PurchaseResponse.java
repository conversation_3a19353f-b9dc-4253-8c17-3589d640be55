package keleshteri.clinic.management.pharmacy.purchase.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

//@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PurchaseResponse {

    private Long id;

    //InvoiceDate
    private  String invoiceDatePersian;

    //invoiceDate
    private LocalDate invoiceDate;

    //Supplier
    private String supplier;
    private Long supplierId;

    //InventoryPeriod
    private String inventoryPeriod;
    private Long inventoryPeriodId;

    //Comments
    private String comments;

    //PurchaseItem
    private List<PurchaseItemResponse> items;

    //total
    private BigDecimal total;

//    public List<PurchaseItem> getPurchaseItemList() {
////        return purchaseItemList;
//        return purchaseItemList == null ? null : new ArrayList<>(purchaseItemList);
//    }
//
//    public void setPurchaseItemList(List<PurchaseItem> purchaseItemList) {
////        this.purchaseItemList = purchaseItemList;
//        if (purchaseItemList == null) {
//            this.purchaseItemList = null;
//        } else {
//            this.purchaseItemList = Collections.unmodifiableList(purchaseItemList);
//        }
//    }
}
