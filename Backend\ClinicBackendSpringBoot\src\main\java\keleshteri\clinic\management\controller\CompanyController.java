package keleshteri.clinic.management.controller;

import keleshteri.clinic.management.model.Company;
import keleshteri.clinic.management.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/companies")
public class CompanyController {


    private final CompanyService companyService;

    @Autowired
    public CompanyController(CompanyService companyService) {
        this.companyService = companyService;
    }

    /**
     * Get All Products pagination
     */
    @GetMapping
    public ResponseEntity<List<Company>> getAll(){
        return companyService.pagination();
    }

    /**
     * Get a Product  BY Id
     */
    @GetMapping("/{id}")
    public ResponseEntity<Company> getById(@PathVariable Long id){
        return ResponseEntity.ok(companyService.find(id));
    }

    /**
     * Create a Product
     */
    @PostMapping
    public ResponseEntity<Company> create(@Valid @RequestBody Company company){
        return ResponseEntity.ok(companyService.create(company));
    }

    /**
     * Update a Company
     */
    @PutMapping("/{id}")
    public ResponseEntity<Company> update(@PathVariable Long id, @RequestBody Company companyDetails){
        return ResponseEntity.ok(companyService.update(id,companyDetails));
    }

    /**
     * Delete a Company
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> delete(@PathVariable Long id){
        return ResponseEntity.ok(companyService.delete(id));
    }

}
