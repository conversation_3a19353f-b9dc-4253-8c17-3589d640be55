package keleshteri.clinic.management.patient.helper;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Stream;

import keleshteri.clinic.management.enums.WeekDay;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.enums.Gender;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

public class PatientExcelHelper {

    public static String TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    static String[] HEADERs = {
            "fileNumber",
            "nationalId",
            "firstName",
            "lastName",
            "fileDate",
            "day",
            "gender",
            "email",
            "birthDatePersian",
            "homeAddress",
            "landlineNumberHome",
            "landlineNumberOffice",
            "cellphoneNumber",
            "confirm",
            "deleted"
    };
    static String SHEET = "Patients";

    public static boolean hasExcelFormat(MultipartFile file) {

        if (!TYPE.equals(file.getContentType())) {
            return false;
        }

        return true;
    }

    public static List<Patient> excelToPatients(InputStream is) {
        try {
            Workbook workbook = new XSSFWorkbook(is);

            Sheet sheet = workbook.getSheet(SHEET);
            Iterator<Row> rows = sheet.iterator();

            List<Patient> patientList = new ArrayList<Patient>();

            int rowNumber = 0;
            while (rows.hasNext()) {
                Row currentRow = rows.next();

                // skip header
                if (rowNumber == 0) {
                    rowNumber++;
                    continue;
                }

                Iterator<Cell> cellsInRow = currentRow.iterator();

                Patient patient = new Patient();

                int cellIdx = 0;
                while (cellsInRow.hasNext()) {
                    Cell currentCell = cellsInRow.next();

                    switch (cellIdx) {
                        case 0:
                            //fileNumber
                            patient.setFileNumber(mustBeLong(currentCell));
                            break;
                        case 1:
                            //NationalId
                            patient.setNationalId(mustBeString(currentCell));
                            break;
                        case 2:
                            //firstName
                            patient.setFirstName(mustBeString(currentCell));
                            break;
                        case 3:
                            //lastName
                            patient.setLastName(mustBeString(currentCell));
                            break;
                        case 4:
                            //fileDate
                            System.out.println(mustBeString(currentCell));
                            System.out.println(mustBeString(currentCell).isEmpty());
                            if(!mustBeString(currentCell).isEmpty()){
                                patient.setFileDate(mustBeString(currentCell));
                            }

                            break;
                        case 5:
                            //day
                            System.out.println(mustBeString(currentCell));
                            patient.setDay(Stream.of(WeekDay.values()).filter(targetEnum -> targetEnum.getLabel().equals(mustBeString(currentCell))).findFirst().orElse(null));
                            break;
                        case 6:
                            //gender
                            System.out.println(mustBeString(currentCell));
                            if(!mustBeString(currentCell).isEmpty()){
                                patient.setGender(Stream.of(Gender.values()).filter(targetEnum -> targetEnum.getLabel().equals(mustBeString(currentCell))).findFirst().orElse(null));
                            }else {
                                patient.setGender(Stream.of(Gender.values()).filter(targetEnum -> targetEnum.name().equals("OTHER")).findFirst().orElse(null));
                            }
                            break;
                        case 7:
                            //email
                            if( mustBeString(currentCell) != null && mustBeString(currentCell).isEmpty()==false) {
                                patient.setEmail(mustBeString(currentCell));
                            }
                            break;
                        case 8:
                            //birthDatePersian
                            if(mustBeString(currentCell) != null && mustBeString(currentCell).isEmpty()==false){
                                patient.setBirthDatePersian(mustBeString(currentCell));
                            }
                            break;
                        case 9:
                            //homeAddress
                            patient.setHomeAddress(mustBeString(currentCell));
                            break;
                        case 10:
                            //landlineNumberHome
                            patient.setLandlineNumberHome(mustBeString(currentCell));
                            break;
                        case 11:
                            //landlineNumberOffice
                            patient.setLandlineNumberOffice(mustBeString(currentCell));
                            break;
                        case 12:
                            //cellphoneNumber
                            patient.setCellphoneNumber(mustBeString(currentCell));
                            break;
                        case 13:
                            //confirm
                            patient.setConfirm(currentCell.getBooleanCellValue());
                            break;
                        case 14:
                            //deleted
                            patient.setDeleted(currentCell.getBooleanCellValue());
                            break;
                        default:
                            break;
                    }

                    cellIdx++;
                }

                patientList.add(patient);
            }

            workbook.close();

            return patientList;
        } catch (IOException e) {
            throw new RuntimeException("fail to parse Excel file: " + e.getMessage());
        }
    }


    //-----
    public static String convert(double number){
        return Double.toString(number);
    }

    //must be String
    private static  String mustBeString (Cell cell){
        String data= null;
        if(cell.getCellType()==CellType.NUMERIC){
            data = convert(cell.getNumericCellValue());
        }else if(cell.getCellType()==CellType.STRING){
            data = cell.getStringCellValue();
        }
        return data;
    }

    //must be Long
    private static Long mustBeLong(Cell cell){
        Long data = 0L;

        if (cell.getCellType()==CellType.NUMERIC){
            data = (long) cell.getNumericCellValue();
        }else if(cell.getCellType()== CellType.STRING){
            data =  Long.getLong(cell.getStringCellValue());
        }

        return data;
    }
}
