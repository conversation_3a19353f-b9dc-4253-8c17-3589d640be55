package keleshteri.clinic.management.pharmacy.inventory.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.model.BaseEntity;
import keleshteri.clinic.management.product.model.Product;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(
        name = "inventory_items",
        uniqueConstraints={@UniqueConstraint(columnNames ={"product","period"})}
)
public class InventoryItem extends BaseEntity {

    //Product
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "product",nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
//    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    @JsonIgnore
    private Product product;

    //InventoryPeriod
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "period",nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    @JsonIgnore
    private  InventoryPeriod period;

    //Purchase cost
    @NotNull(message = "قیمت خرید")
    @Column(name = "purchase_price")
    private BigDecimal purchasePrice;

    //salePrice
    @NotNull(message = "قیمت فروش")
    @Column(name = "sale_price")
    private BigDecimal salePrice;

    //salePremiumPrice
    @NotNull(message = "قیمت آزاد وارد کنید")
    @Column(name = "sale_premium_price")
    private BigDecimal salePremiumPrice;

    @NotNull(message = "تاریخ انقضا دارو")
    @Column(name = "expiration_date")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "تاریخ انقضا دارو"
    )
    private  String expirationDate;

    //expirationDateEnglish
    @Column(name = "expiration_date_english")
    private LocalDate expirationDateEnglish;

    //Balance start
    @Column(name = "balance_start")
    private Double balance=0.0;


    /***
     *   Setter
     */
    public void setExpirationDate(String expirationDate) {
        if(this.expirationDateEnglish == null){
            PersianDate persianDate = PersianDate.parse(expirationDate);
            LocalDate DateConverted = persianDate.toGregorian();
            this.expirationDateEnglish =   DateConverted;
        }
        this.expirationDate = expirationDate;
    }
    /***
     * Getter
     */
    public String getProductName() {
        return product.getName();
    }

    /***
     *
     *Constructor
     */
    public InventoryItem(
            Product productVariant,
            InventoryPeriod period,
            BigDecimal purchasePrice,
            BigDecimal salePrice,
            BigDecimal salePremiumPrice,
            String expirationDate,
            Double balance) {
        this.product = productVariant;
        this.period = period;
        this.purchasePrice = purchasePrice;
        this.salePrice = salePrice;
        this.salePremiumPrice = salePremiumPrice;
        //--------
        if(this.expirationDateEnglish == null){
            PersianDate persianDate = PersianDate.parse(expirationDate);
            LocalDate DateConverted = persianDate.toGregorian();
            this.expirationDateEnglish =   DateConverted;
        }
        //---
        this.expirationDate = expirationDate;
        this.balance = balance;
    }
}
