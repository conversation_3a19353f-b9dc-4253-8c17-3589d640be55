package keleshteri.clinic.management.payment.service;

import keleshteri.clinic.management.enums.PaymentStatus;
import keleshteri.clinic.management.enums.PaymentTransactionStatus;
import keleshteri.clinic.management.exception.RecordExistsException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.model.Payment;
import keleshteri.clinic.management.model.PaymentTransaction;
import keleshteri.clinic.management.payment.repository.PaymentRepository;
import keleshteri.clinic.management.payment.request.PaymentTransactionRequest;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryItemService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryOriginalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class PaymentService {

    @Autowired
    private PaymentRepository repository;

    @Autowired
    private InventoryOriginalService inventoryService;

    @Autowired
    private InventoryItemService itemService;

    /***
     * find   Payment
     */
    public Payment find(Long id){
        Payment payment = repository.findById(id)
                .orElseThrow(()-> new ResourceNotFoundException("Payment not find by id:"+id));
        return payment;
    }

    /***
     *  All
     ***/
    public List<Payment> all(){
        List<Payment> payments=repository.findAll();
        return payments;
    }

    /**
     * All Pagination
     * @return
     */
    public ResponseEntity<List<Payment>> pagination() {
        List<Payment> payments=repository.findAll();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "payments 0-"+payments.size() + "/" + payments.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(payments);
    }
    /**
     * all Pagination PaymentStatus
     */
    public ResponseEntity<List<Payment>> pagination(PaymentStatus paymentStatus) {
        List<Payment> payments=repository.findByPaymentStatus(paymentStatus);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "payments 0-"+payments.size() + "/" + payments.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(payments);
    }

    /***
     *
     * @param  payment
     * @return payment
     */
    public Payment create(Payment payment){
        return repository.save(payment);
    }

    /**
     *
     * @return
     */
    public Payment createPaymentTransaction(Payment payment,List<PaymentTransactionRequest> request) {

        //
        //get the list of transaction
        AtomicInteger index2= new AtomicInteger(1);
        List<PaymentTransaction> newTransactionsList = new ArrayList<>();
        request.forEach(transaction->{
            log.info("create newTransactionsList({}) " +
                            "TransactionType: {} ," +
                            "Method: {}  ," +
                            "transaction Amount: {} ,",
                    index2,
                    transaction.getPaymentTransactionType().getLabel(),
                    transaction.getPaymentMethod().getLabel(),
                    transaction.getAmount());
            //
            newTransactionsList.add(
                    new PaymentTransaction(
                            payment,//payment,
                            transaction.getPaymentTransactionType(),//paymentTransactionType,
                            PaymentTransactionStatus.COMPLETED,//paymentTransactionStatus,
                            transaction.getPaymentMethod(),//paymentMethod,
                            transaction.getAmount()//amount
                    )
            );
            index2.getAndIncrement();
        });
        //
        //check amount of Transaction and payment
        BigDecimal totalNewTransactions = newTransactionsList.stream().map(t->t.getAmount())
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        log.info("Total amount of new transactions , Amount: {}",totalNewTransactions);
        //check before Transactions
        //if payment has debt default 0
        BigDecimal debt =BigDecimal.ZERO;
        log.info("create debt is zero: {}",debt);

        //log payment
        log.info("Payment id: {}, " +
                        "Payment Amount: {} ," +
                        "Debt amount: {}," +
                        "Payment Status: {} ," +
                        "Transactions count :{}",
                payment.getId(),
                payment.getAmount(),
                payment.getDebtAmount(),
                payment.getPaymentStatus().getLabel(),
                payment.getTransactions().size()
        );
        //log Transactions of payment
        AtomicInteger index= new AtomicInteger(1);
        //check if payment has Transactions handle not more than amount payment
        if(payment.getTransactions().size()>0){
            //

            //log
            payment.getTransactions().forEach((transaction)->{
                log.info("transaction({}) Amount: {} type: {} Method:{} Status:{}",
                        index,
                        transaction.getAmount(),
                        transaction.getPaymentTransactionType().getLabel(),
                        transaction.getPaymentMethod().getLabel(),
                        transaction.getPaymentTransactionStatus()
                );
                index.getAndIncrement();
            });
            //
            log.info("sum of transactions Amount :{} ," +
                            " amount of Payment : {} ," +
                            " if transactions Amount great than payment amount {} ",
                    payment.getSumAmountTransactions(),
                    payment.getAmount(),
                    (payment.getSumAmountTransactions().doubleValue() >  payment.getAmount().doubleValue()) ? "YES":"NO"
                    );
            //check Transactions amounts not great  or equal stop to go next
            if(payment.getSumAmountTransactions().doubleValue() >= payment.getAmount().doubleValue()){
                log.info("this payment is full paid .. paymentAmount:{}" +
                        " ,TransactionsAmount:{} ",
                        payment.getAmount(),
                        payment.getSumAmountTransactions());
                throw new RecordExistsException("این صورت حساب به طور کامل پرداخت شده است صورتحساب مبلغ:" +
                        payment.getAmount()+
                        " ریال "+
                        "و مجموع پرداختی ها مبلغ:" +
                        payment.getSumAmountTransactions() +
                        " ریال ");
            }
            //check if newTransactionsList amount not be great then all received
            log.info("Transactions totalNew :{}, totalPaid: {}, SumTotal: {}",
                    totalNewTransactions.doubleValue(),
                    payment.getSumAmountTransactions().doubleValue(),
                    totalNewTransactions.add(payment.getSumAmountTransactions()));
            BigDecimal SumTotalNewPaid =totalNewTransactions.add(payment.getSumAmountTransactions());
             if(SumTotalNewPaid.doubleValue() > payment.getAmount().doubleValue()){
                log.info("total  Transactions new not paid and Paid :{} " +
                        "great then payment amount: {}",
                        SumTotalNewPaid,
                        payment.getAmount()
                        );
                throw new RecordExistsException("این مبلغ پرداختی " +
                        totalNewTransactions +
                        " ریال " +
                        "و مجموع پرداختی ها قبل برای این صورتحساب " +
                        payment.getSumAmountTransactions() +
                        " ریال است " +
                        " که از مبلغ صورتحساب " +
                        payment.getAmount() +
                        " ریال " +
                        "می باشد بیشتر می شود " +
                        "مبلغ بدهی این صورتحساب مبلغ: " +
                        payment.getAmount().subtract(payment.getSumAmountTransactions()) +
                        " ریال می باشد." +
                        "");
            }
             //check debt
            log.info("payment Amount: {} - {}={}",payment.getAmount(),payment.getSumAmountTransactions(),payment.getAmount().subtract(payment.getSumAmountTransactions()));
            debt=payment.getAmount().subtract(payment.getSumAmountTransactions());
             log.info("debt is : {}",debt);
        }
        //-----check if sum amount transaction same or smaller than payment amount


        //if  PaymentStatus Pending or DEBT
        log.info("check PaymentStatus : {}",payment.getPaymentStatus().getLabel());
        if(
                payment.getPaymentStatus().equals(PaymentStatus.Pending)
                ||
                payment.getPaymentStatus().equals(PaymentStatus.DEBT)
        ){

            //different totalNewTransactions amount of new transactions with payment Amount
            BigDecimal addNewTotalWithOld= payment.getSumAmountTransactions().add(totalNewTransactions);
            log.info("add NewTotal {} With OldTotal {} Transaction = {}",totalNewTransactions,payment.getSumAmountTransactions(),addNewTotalWithOld);

            //
//            BigDecimal different= payment.getAmount().subtract(totalNewTransactions);
            BigDecimal different= payment.getAmount().subtract(addNewTotalWithOld);

            log.info("Different between  payment Amount: {} and new transactions Amount {} is {}",payment.getAmount(),totalNewTransactions,different);
            //check
            log.info("different totalNewTransactions amount with payment Amount > than 0 : {}" ,(different.doubleValue()>0)?"Yes":" No");

            if(different.doubleValue()>0){
                payment.setDebtAmount(different);
                payment.setPaymentStatus(PaymentStatus.DEBT);
            }else if(different.doubleValue()==0) {
                payment.setDebtAmount(different);
                payment.setPaymentStatus(PaymentStatus.PAID);
            }

            payment.getTransactions().addAll(newTransactionsList);

            Payment savePayment =repository.save(payment);

//       /****
//         * create Inventory
//         ****/
//            savePayment.getSale().getItems().forEach(orItem -> {
//
//            //save each
//            inventoryService.create(new InventoryOriginal(
//                    InventoryType.REMOVAL,
//                    itemService.find(orItem.getProductVariant(),savePayment.getSale().getPeriod()),
//                    transaction,
//                    orItem.getQuantity(),
//                    orItem.getTotal(),
//                    savePayment.getSale().getInvoiceDatePersian()
//            ));
//        });

            //save
            return savePayment;

        }else {
            throw new RecordExistsException("this payment is paid");
        }



    }
}
