package keleshteri.clinic.management.pharmacy.request;

import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.pharmacy.virtual.SelectOptions;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class MedicineProductRequest {

    @NotNull(message = "Please enter  code")
    private Integer code;

    @NotNull(message = "نام کالا دارویی")
    private String name;

    @NotNull(message = "نام دارو")
    private Long medicine;

    @NotNull(message = "شرکت دارویی")
    private Long company;

    @NotNull(message = "نوع کالا دارویی")
    private Long type;

    @NotNull(message = "واحد اندازه گیری")
    private UnitsMeasurement units;

    //selectOptions
//    private Map<String,Object> variantList;
//    private List<Map<String,Object>> variantList;

    private List<SelectOptions> variantList;

}
