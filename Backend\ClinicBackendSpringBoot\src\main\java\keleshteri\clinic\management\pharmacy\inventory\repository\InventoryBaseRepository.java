package keleshteri.clinic.management.pharmacy.inventory.repository;

import keleshteri.clinic.management.enums.InventoryType;
import keleshteri.clinic.management.pharmacy.inventory.model.*;
import keleshteri.clinic.management.pharmacy.virtual.InventoryBalance;
import keleshteri.clinic.management.pharmacy.virtual.InventoryMedicineBalance;
import keleshteri.clinic.management.pharmacy.virtual.InventoryInventoryItemBalance;
import keleshteri.clinic.management.pharmacy.virtual.InventoryProductBalance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import java.util.List;

@NoRepositoryBean
public interface InventoryBaseRepository<T extends Inventory> extends JpaRepository<T, Long> {


    //if has this item in Inventory
    boolean existsByInventoryItem(InventoryItem inventoryItem);

    //if has this item in Inventory by InventoryType
    boolean existsByInventoryItemAndInventoryType(InventoryItem inventoryItem, InventoryType inventoryType);

    /** get List Of Inventory by Period**/
//    List<T> findByPeriod(InventoryPeriod period);

     @Query("SELECT c " +
                  "FROM #{#entityName} c, " +
                  "InventoryItem i   " +
                "WHERE c.inventoryItem = i  AND i.period = :period" )
    List<T> findByPeriod(
             @Param("period") InventoryPeriod period
     );

        /** Get Page of Inventory by Period **/
//    Page<T> findByPeriod(InventoryPeriod period, Pageable pageable);
        @Query("SELECT c " +
                "FROM #{#entityName} c, " +
                "InventoryItem i   " +
                "WHERE c.inventoryItem = i  AND i.period = :period" )
        Page<T> findByPeriod(
                @Param("period") InventoryPeriod period,
                Pageable pageable
        );

    ///Sum of qty this Product with type
    @Query("SELECT " +
            "SUM(c.quantity)  " +
            "FROM #{#entityName} c " +
            "WHERE c.inventoryItem = :product and c.inventoryType= :type" )
    Double sumByProductQuantity(
            @Param("product") InventoryItem inventoryItem,
            @Param("type") InventoryType inventoryType
    );

    //Sum of qty this Product with  group Type
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryBalance(" +
            "SUM(c.quantity) AS balance," +
            "c.inventoryType AS inventoryType" +
            ")  " +
            "FROM #{#entityName} c " +
            "WHERE c.inventoryItem = :product " +
            "GROUP BY c.inventoryType" )
    List<InventoryBalance> sumQuantityGroupType(
            @Param("product") InventoryItem inventoryItem
    );

    //sum of Medicine
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryMedicineBalance(" +
            "m AS medicine ," +
            "SUM(s.quantity) AS balance, " +
            "p.type as type" +
            ")  " +
            "FROM #{#entityName} s " +
            "INNER JOIN s.inventoryItem i " +
            "INNER JOIN i.product p " +
            "INNER JOIN p.medicine m "+
            "GROUP BY m"
          )
    List<InventoryMedicineBalance> sumQuantityMedicine();

    //sum of Medicine
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryMedicineBalance(" +
            "m AS medicine ," +
            "SUM(s.quantity) AS balance, " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.ADDITION) then s.quantity else 0 end) AS quantityAdd ,  " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.REMOVAL) then s.quantity else 0 end) AS quantityRemove ,  " +
            "p.type as type" +
            ")  " +
            "FROM #{#entityName} s " +
            "INNER JOIN s.inventoryItem i " +
            "INNER JOIN i.product p " +
            "INNER JOIN p.medicine m "+
            "WHERE i.period = :period " +
            " GROUP BY m,p.type"
    )
    List<InventoryMedicineBalance> sumQuantityMedicine(
            @Param("period") InventoryPeriod period
    );

    //sum of  QuantityProduct
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryProductBalance(" +
            "p AS product ," +
            "SUM(s.quantity) AS balance, " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.ADDITION) then s.quantity else 0 end) AS quantityAdd ,  " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.REMOVAL) then s.quantity else 0 end) AS quantityRemove   " +
            ")  " +
            "FROM #{#entityName} s " +
            "INNER JOIN s.inventoryItem i " +
            "INNER JOIN i.product p " +
            "WHERE i.period = :period " +
            " GROUP BY p"
    )
    List<InventoryProductBalance> sumQuantityProduct(
            @Param("period") InventoryPeriod period
    );
    //sum of  QuantityProduct
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryProductBalance(" +
            "p.genericName AS genericName ," +
            "SUM(s.quantity) AS balance, " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.ADDITION) then s.quantity else 0 end) AS quantityAdd ,  " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.REMOVAL) then s.quantity else 0 end) AS quantityRemove   " +
            ")  " +
            "FROM #{#entityName} s " +
            "INNER JOIN s.inventoryItem i " +
            "INNER JOIN i.product p " +
//            "INNER JOIN p.genericName g " +
            "WHERE i.period = :period " +
            " GROUP BY p.genericName"
    )
    List<InventoryProductBalance> sumQuantityProductGeneric(
            @Param("period") InventoryPeriod period
    );

    //sum of  QuantityItem
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryInventoryItemBalance(" +
            "i AS item ," +
            "SUM(s.quantity) AS balance, " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.ADDITION) then s.quantity else 0 end) AS quantityAdd ,  " +
            "sum(case when(s.inventoryType = keleshteri.clinic.management.enums.InventoryType.REMOVAL) then s.quantity else 0 end) AS quantityRemove   " +
            ")  " +
            "FROM #{#entityName} s " +
            "INNER JOIN s.inventoryItem i " +
            "WHERE i.period = :period " +
            " GROUP BY i"
    )
    List<InventoryInventoryItemBalance> sumQuantityItem(
            @Param("period") InventoryPeriod period
    );

    ///TypeItem
    @Query(value = "SELECT " +
            "new keleshteri.clinic.management.pharmacy.virtual.InventoryInventoryItemBalance(" +
            "i AS item ," +
            "SUM(s.quantity) AS balance, " +
            "s.inventoryType AS inventoryType" +
            ")  " +
            "FROM #{#entityName} s " +
            "INNER JOIN s.inventoryItem i " +
            "WHERE i.period = :period " +
            " GROUP BY i,s.inventoryType"
    )
    List<InventoryInventoryItemBalance> sumQuantityInventoryTypeItem(
            @Param("period") InventoryPeriod period
    );
}
