package keleshteri.clinic.management.pharmacy.delivery.service;

import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.exception.InventoryException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.prescription.Prescription;
import keleshteri.clinic.management.enums.InventoryType;
import keleshteri.clinic.management.pharmacy.delivery.model.Delivery;
import keleshteri.clinic.management.pharmacy.delivery.model.DeliveryDetails;
import keleshteri.clinic.management.pharmacy.delivery.repository.DeliveryRepository;
import keleshteri.clinic.management.pharmacy.delivery.virtual.DeliveryPrescriptionPatient;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryVirtual;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryItemService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryPeriodService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryOriginalService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryVirtualService;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.service.ProductService;
import keleshteri.clinic.management.pharmacy.request.DeliveredRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class DeliveryService {

    //DeliveryRepository
    private final DeliveryRepository repository;

    @Autowired
    private InventoryOriginalService inventoryOriginalService;

    @Autowired
    private InventoryVirtualService inventoryVirtualService;

    @Autowired
    private InventoryPeriodService periodService;

    @Autowired
    private InventoryItemService itemService;

    @Autowired
    private ProductService productService;

    @Autowired
    public DeliveryService(DeliveryRepository repository) {
        this.repository = repository;

    }

    /**
     * @param id
     * @return Delivery
     */
    public Delivery find(Long id){
        Delivery delivery = repository.findById(id)
                .orElseThrow(()-> new ResourceNotFoundException("تحویل دارو با شناسه وجود ندارد :" + id));
        return delivery;
    }

    //Find all
    public List<Delivery> all() {
        return repository.findAll();
    }

    /** get today **/
    public List<Delivery> todayDelivery(){
        PersianDate today = PersianDate.now();
        List<Delivery> deliveries = repository.findByDeliveryDatePersianAndDelivered(today.toString(),false);
        return deliveries;
    }

    /**
     *
     */
    public List<Delivery> delayedDelivery() {
//        PersianDate today = PersianDate.now();
        LocalDate today = LocalDate.now();
        List<Delivery> deliveries = repository.findByDeliveryDateLessThanAndDelivered(today,false);
        return deliveries;
    }

    //Report
    public ResponseEntity<List<Delivery>> report(String date){
        List<Delivery> delivery = repository.findByDeliveryDatePersian(date);
        return ResponseEntity.ok(delivery);
    }

    /**
     * Delivery ByPrescription  
     */
//    public ResponseEntity<List<Delivery>> pagination(Prescription prescription){
//        List<Delivery> deliveries= repository.findByPrescription(prescription);
//
//        HttpHeaders responseHeaders = new HttpHeaders();
//        responseHeaders.set("Content-Range", "deliveries 0-"+deliveries.size() + "/" + deliveries.size());
//
//        return ResponseEntity.ok()
//                .headers(responseHeaders)
//                .body(deliveries);
//    }




    public ResponseEntity<List<Delivery>> pagination() {
        List<Delivery> medicineDeliveries= repository.findAll();
//        List<Delivery> medicineDeliveries= repository.findByDelivered(false);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "medicineDeliveries 0-"+medicineDeliveries.size() + "/" + medicineDeliveries.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(medicineDeliveries);
    }


    public Delivery create(Delivery delivery) {
        return repository.save(delivery);
    }


    /***
     *
     * @param id
     * @param newDelivery
     * @return
     */
    public Delivery update(Long id, Delivery newDelivery, List<DeliveryDetails> newDeliveryDays) {
        //Delivery
        Delivery findDelivery = this.find(id);
        //
        //set delivered true
        findDelivery.setProduct(newDelivery.getProduct());
        findDelivery.setDeliveredDatePersian(newDelivery.getDeliveredDatePersian());
        findDelivery.setDelivered(newDelivery.getDelivered());
        findDelivery.setDeliveredDose(newDelivery.getDeliveredDose());
        //deliveryDetails
        newDeliveryDays.forEach(day->{
            findDelivery.getDeliveryDetails()
                    .stream().filter(DeliveryDetail-> DeliveryDetail.getId()==day.getId())
                    .findFirst()
                    .get()
                    .setDelivered(day.isDelivered());
        });

        //save all
        return  repository.save(findDelivery);
    }


    public ResponseEntity<Map<String, Boolean>> delete(Long id) {
        return null;
    }

//    @Test
    @Transactional
    public  void deleteByPrescription(Prescription prescription) {
          repository.deleteByPrescription(prescription);
        long deletedRecords = repository.deleteByPrescription(prescription);
//        assertThat(deletedRecords).isEqualTo(1);
    }



    public ResponseEntity<List<Delivery>> findDeliveryByPrescription(Long prescription_id) {
        List<Delivery> delivery = repository.findByPrescriptionId(prescription_id);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "medicineDeliveries 0-"+ delivery.size() + "/" + delivery.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(delivery);
    }

    public Long countDeliveries() {
        return repository.count();
    }

    /*****
     *Medicine Delivered
     *****/
    @Transactional//(rollbackFor = {Exception.class, InvalidSaleException.class})
    public Delivery createDelivered(DeliveredRequest request) {

        /****
         **  Product And
         **
         ****/
        //Find Product
        Product product = productService.find(request.getProduct());

        /***
         * find period or create
         * InventoryPeriod
         * Get the active Period
         ***/
        InventoryPeriod period=null;
        if(request.getInventoryPeriod()!=null){
            period =periodService.find(request.getInventoryPeriod());
        }
        /**
         * find and check if this product has  inventory Item in period
         */
        InventoryItem inventoryItem=itemService.find(product,period);

        /**
         * inventory Item has balance in Inventory
         */
        Double stock = inventoryVirtualService.stockBalance(inventoryItem);
        if(request.getQuantity().doubleValue()>stock){
            throw new InventoryException("موجودی انبار کمتر از مقدار ورودی شما هست");
        }


        /***
         * Delivery updates
         * find the Delivery and updated
         */
        Delivery delivery = this.update(
                request.getDelivery() , //Delivery id
                new Delivery(
                        product,//product,
                        request.getDeliveredDatePersian(), //deliveredDatePersian,
                        request.getDoseDelivered(), //deliveredDose,
                        true  //delivered
                ),
                request.getDeliveryDetails()//days
        );

        //save each
        inventoryVirtualService.save(new InventoryVirtual(
                InventoryType.REMOVAL,
                inventoryItem,
                null,//delivery,
                request.getQuantity(),
                inventoryItem.getSalePrice(),
                request.getDeliveredDatePersian()
        ));
        //

        return delivery;
    }

    /**
     * Delivery ByPrescription
     */

    public List<DeliveryPrescriptionPatient> getByPrescriptionPatientAll(Patient patient) {
        List<DeliveryPrescriptionPatient> deliveryPrescriptionPatients =repository.getByPrescriptionPatientAll(patient);

        deliveryPrescriptionPatients.forEach(delivery->{

            if(delivery.getProductId()!=null){
                delivery.setProduct(productService.find(delivery.getProductId()));
                System.out.println(delivery.getProductId());
            }
        });
        return deliveryPrescriptionPatients;
    }

    public List<DeliveryPrescriptionPatient> getByPrescriptionPatientsAll() {
        return repository.getByPrescriptionPatientsAll();
    }


}
