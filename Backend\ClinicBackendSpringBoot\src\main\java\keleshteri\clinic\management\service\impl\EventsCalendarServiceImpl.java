package keleshteri.clinic.management.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import keleshteri.clinic.management.enums.CalendarType;
import keleshteri.clinic.management.model.EventsCalendar;
import keleshteri.clinic.management.repository.EventsCalendarRepository;
import keleshteri.clinic.management.service.EventsCalendarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Service
public class EventsCalendarServiceImpl implements EventsCalendarService {

    private final EventsCalendarRepository repository;

    @Autowired
    public EventsCalendarServiceImpl(EventsCalendarRepository repository) {
        this.repository = repository;
    }

    @Override
    public List<EventsCalendar> find(CalendarType type, int month, int day) {
        return repository.findByCalendarTypeAndMonthAndDay(type,month,day);
    }

    public Iterable<EventsCalendar> saveAll(List<EventsCalendar> eventsCalendars) {
        return repository.saveAll(eventsCalendars);
    }
    /**
     * seeder
     */
    @Override
    public void seeder() {
        if(repository.count()==0){
            // read json and write to db
            ObjectMapper mapper = new ObjectMapper();
            TypeReference<List<EventsCalendar>> typeReference = new TypeReference<List<EventsCalendar>>(){};
            InputStream inputStream = TypeReference.class.getResourceAsStream("/json/events.json");
            try {
                List<EventsCalendar> users = mapper.readValue(inputStream,typeReference);
                this.saveAll(users);
                System.out.println("Events Saved!");
            } catch (IOException e){
                System.out.println("Unable to save Event: " + e.getMessage());
            }
        }
    }
}
