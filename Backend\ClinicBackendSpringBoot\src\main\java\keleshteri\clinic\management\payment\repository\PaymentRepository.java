package keleshteri.clinic.management.payment.repository;

import keleshteri.clinic.management.enums.PaymentStatus;
import keleshteri.clinic.management.model.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaymentRepository extends JpaRepository<Payment,Long> {

    List<Payment> findByPaymentStatus(PaymentStatus paymentStatus);
}
