package keleshteri.clinic.management.pharmacy.sale.payload;


import keleshteri.clinic.management.payment.request.PaymentTransactionLimitRequest;
import lombok.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;


@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class SaleDateUpdateRequest {

    //InvoiceDate
    @NotNull(message = "Please enter  invoice date")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "invoice_date"
    )
    private  String invoiceDatePersian;
}
