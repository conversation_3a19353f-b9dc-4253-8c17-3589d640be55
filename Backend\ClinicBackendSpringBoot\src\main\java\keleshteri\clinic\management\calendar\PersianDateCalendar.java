package keleshteri.clinic.management.calendar;

import keleshteri.clinic.management.calendar.persian.AlgorithmicConverter;
import keleshteri.clinic.management.calendar.persian.LookupTableConverter;

public class PersianDateCalendar extends AbstractDate {

    public PersianDateCalendar(int year, int month, int dayOfMonth) {
        super(year, month, dayOfMonth);
    }

    public PersianDateCalendar(long jdn) {
        super(jdn);
    }

    public PersianDateCalendar(AbstractDate date) {
        super(date);
    }

    // Converters
    @Override
    public long toJdn() {
        long result = LookupTableConverter.toJdn(getYear(), getMonth(), getDayOfMonth());
        return result == -1 ? AlgorithmicConverter.toJdn(getYear(), getMonth(), getDayOfMonth()) : result;
    }

    @Override
    protected int[] fromJdn(long jdn) {
        int[] result = LookupTableConverter.fromJdn(jdn);
        return result == null ? AlgorithmicConverter.fromJdn(jdn) : result;
    }
}
