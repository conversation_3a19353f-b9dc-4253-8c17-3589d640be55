package keleshteri.clinic.management.pharmacy.virtual;

import com.github.mfathi91.time.PersianDate;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MaxPeriodDate {

    Integer period;
    PersianDate date;

    //

    public Integer getPeriodPlus() {
        return period+1;
    }

    //

    public MaxPeriodDate() {
    }

    public MaxPeriodDate(Integer period, String date) {
        this.period = period;
        this.date = PersianDate.parse(date);
    }
}
