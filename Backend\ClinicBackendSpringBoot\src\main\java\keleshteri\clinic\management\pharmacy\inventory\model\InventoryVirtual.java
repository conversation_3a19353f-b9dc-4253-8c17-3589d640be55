package keleshteri.clinic.management.pharmacy.inventory.model;

import keleshteri.clinic.management.payment.interfaces.SourceInventoryTransaction;
import keleshteri.clinic.management.enums.InventoryType;
import keleshteri.clinic.management.pharmacy.delivery.model.Delivery;
import keleshteri.clinic.management.pharmacy.purchase.model.Purchase;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Any;
import org.hibernate.annotations.AnyMetaDef;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.MetaValue;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "inventories_virtual")
public class InventoryVirtual extends Inventory implements Serializable {

    //source
    @Any(metaDef = "source_virtual", metaColumn = @Column(name = "source_virtual_type"), fetch = FetchType.LAZY)
    @AnyMetaDef(
            name = "source_virtual",
            metaType = "string",
            idType = "long",
            metaValues = {
                    @MetaValue(value = "Purchase", targetEntity = Purchase.class),
                    @MetaValue(value = "Delivery", targetEntity = Delivery.class)
            }
    )
    @Cascade( { org.hibernate.annotations.CascadeType.ALL})
    @JoinColumn(name = "source_virtual_id")
    private SourceInventoryTransaction source;



    public InventoryVirtual(
            InventoryType inventoryType,
            InventoryItem inventoryItem,
            SourceInventoryTransaction source,
            Double quantity,
            BigDecimal price,
            String datePersian) {
        super(inventoryType, inventoryItem, quantity, price, datePersian);
        //
        this.source=source;
    }

    public InventoryVirtual(InventoryType inventoryType, InventoryItem inventoryItem, SourceInventoryTransaction source, Double quantity, Double onHoldQuantity, boolean onHold, BigDecimal price, String datePersian) {
        super(inventoryType, inventoryItem, quantity, onHoldQuantity, onHold, price, datePersian);
        this.source=source;
    }

    public InventoryVirtual(InventoryType inventoryType, InventoryItem inventoryItem, SourceInventoryTransaction source, Double quantity, Double onHoldQuantity, boolean onHold, BigDecimal price, String datePersian, LocalDate date) {
        super(inventoryType, inventoryItem, quantity, onHoldQuantity, onHold, price, datePersian, date);
        this.source=source;
    }
}
