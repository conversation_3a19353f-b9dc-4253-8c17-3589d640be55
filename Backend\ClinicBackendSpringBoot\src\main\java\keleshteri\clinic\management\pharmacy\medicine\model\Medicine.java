package keleshteri.clinic.management.pharmacy.medicine.model;


import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.model.BaseEntity;
import keleshteri.clinic.management.pharmacy.medicine.enums.MedicineType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "medicines")
public class Medicine extends BaseEntity {

    /**
     * Code
     */
    @Column(nullable = false,unique = true)
    private Integer code;

    /**
     * Name
     */
    @NotNull(message = "Please enter  Name")
    @Column(unique = true,nullable = false)
    private String name;

    /**
     * Description
     */
    @Column(name = "description")
    private String description;

    /**
     * Medicine has Types
     */
    @ElementCollection(targetClass = MedicineType.class)
    @CollectionTable(
            name = "medicine_has_types",
            joinColumns = @JoinColumn(name = "medicine")
    )
    @Column(name = "medicine_type")
    private Set<MedicineType> types = new HashSet<>();

    //basePrice
    @NotNull(message = "تعرفه فروش اولیه")
    @Column(name = "tariff_price")
    private BigDecimal tariffPrice=BigDecimal.ZERO;

    /**
     * medicine has units
     */
    @ElementCollection(targetClass = UnitsMeasurement.class)
    @CollectionTable(
            name = "medicine_has_units",
            joinColumns = @JoinColumn(name = "medicine")
    )
    @Column(name = "units")
    private Set<UnitsMeasurement> units  = new HashSet<>();

    /**
     * Getter
     */
    public UnitsMeasurement getUnit() {
        UnitsMeasurement units =null;
        if(this.name.equals("متادون")){
            units=  this.units.stream().filter(u->u.getCode().equals("milligram")).findFirst().get();
        }else {
            units= this.units.stream().findFirst().get();
        }
        return units;
    }

    public String getUnitLabel() {
        UnitsMeasurement units =null;
        if(this.name.equals("متادون")){
            units=  this.units.stream().filter(u->u.getCode().equals("milligram")).findFirst().get();
        }else {
            units= this.units.stream().findFirst().get();
        }
        return units.getLabel();
    }



    /**
     * Constructor
     */
    public Medicine(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public Medicine(
            Integer code,
            String name,
            String description,
            Set<MedicineType> types,
            Set<UnitsMeasurement> units) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.types = types;
        this.units=units;
    }

    /**
     *
     */
    public String getLabel() {

        return  this.name +" "+ types.stream().map(p->p.getLabel()).collect(Collectors.toList());
    }
}
