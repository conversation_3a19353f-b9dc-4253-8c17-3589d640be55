package keleshteri.clinic.management.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import keleshteri.clinic.management.exception.InvalidDataException;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
//@JsonFormat(shape = JsonFormat.Shape.STRING)
@Getter
public enum WeekDay {

    SATURDAY("Saturday","شنبه") ,
    SUNDAY("Sunday","یکشنبه"),
    MONDAY("Monday","دوشنبه"),
    TUESDAY("Tuesday","سه شنبه")  ,
    WEDNESDAY("Wednesday","چهارشنبه") ,
    THURSDAY("Thursday","پنجشنبه") ,
    FRIDAY("Friday","جمعه") ;

    public final String code;
    public final String label;

    private WeekDay(String code,String label){
        this.code = code;
        this.label= label;
    }

    @JsonCreator
    public static WeekDay decode(final String name) {
        return Stream.of(WeekDay.values()).filter(targetEnum -> targetEnum.name().equals(name)).findFirst().orElseThrow(()-> new InvalidDataException("روز  "+name+" وارد شده با دیتا ما مطابقت ندارد. "));
    }

    @JsonValue
    public String getName() {
        return this.name();
    }
//    @JsonValue
//    public String getLabelFarsi() { return this.labelFarsi;}
//    @JsonValue
//    public Map<String,Object> getLabel() {
//        Map<String,Object> list= new HashMap<>();
//        list.put("name",this.name());
//        list.put("code",this.code);
//        list.put("label",this.label);
//        return list;
//    }

}

