package keleshteri.clinic.management.pharmacy.request;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class ProductAttributeValueRequest {

    //MedicineProduct
    @NotNull(message = "لطفا محصول را وارد کنید")
    private Long product;


    //medicineAttribute
    @NotNull(message = "Please enter  medicineAttribute")
    private  Long attribute;

    //value
    @NotNull(message = "Please enter  value")
    private String value;




//    //text_value
//    private String text_value;
//
//    private Long integer_value;
//
//    private Boolean boolean_value;
//
//    private Double double_value;
//
//    private Date datetime_value;
//
//    private Date date_value;
//
//    private Map<String, Object> json_value;

}
