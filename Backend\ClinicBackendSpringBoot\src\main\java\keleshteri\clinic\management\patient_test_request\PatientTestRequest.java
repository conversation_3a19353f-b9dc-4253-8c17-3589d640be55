package keleshteri.clinic.management.patient_test_request;

import keleshteri.clinic.management.enums.PeriodType;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.laboratory.model.LaboratoryTest;
import keleshteri.clinic.management.model.BaseEntity;
import lombok.Data;
import javax.persistence.*;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;

@Data
@Table(
        name = "patient_test_request",
        uniqueConstraints={@UniqueConstraint(columnNames ={"patient_id","laboratory_test_id","test_date_persian"})}
)
@Entity
public class PatientTestRequest extends BaseEntity {

    /**
     * patient
     *
     */
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "patient_id",nullable = false)
    private Patient patient;

    /**
     * laboratoryTest
     */
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "laboratory_test_id",nullable = false)
    private LaboratoryTest laboratoryTest;

    /**
     * requestDatePersian
     */
    @Pattern(regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$", message = "birthDatePersian")
    @Column(name = "request_date_persian",nullable = false)
    private String requestDatePersian;

    /**
     * requestDate
     */
    @Column(nullable = false)
    private LocalDate requestDate;

    /**
     * testDatePersian
     */
    @Pattern(regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$", message = "birthDatePersian")
    @Column(name = "test_date_persian",nullable = true)
    private String testDatePersian;

    /**
     * testDate
     */
    @Column(nullable = true)
    private LocalDate testDate;

    private PeriodType periodType;
    /**
     * period test
     */
    @Column(nullable = false)
    private long periodTest;


    /**
     * testDatePersian
     */
    @Pattern(regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$", message = "birthDatePersian")
    @Column(name = "test_expired_date_persian",nullable = true)
    private String testExpiredDatePersian;

    /**
     * testDate
     */
    @Column(nullable = true)
    private LocalDate testExpiredDate;

    /**
     * test Expired
     */
    private boolean expired=false;

    //notification
    private boolean notification=true;

    //reminder
    private boolean reminder = true;

}