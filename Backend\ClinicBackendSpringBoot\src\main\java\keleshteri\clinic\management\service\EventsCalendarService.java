package keleshteri.clinic.management.service;

import keleshteri.clinic.management.enums.CalendarType;
import keleshteri.clinic.management.model.EventsCalendar;

import java.util.List;

public interface EventsCalendarService {

    /**
     * find event by month and day and calendar
     */
    List<EventsCalendar> find(CalendarType type,int month,int day);

    /**
     * save a list
     * @param eventsCalendars
     */
    Iterable<EventsCalendar> saveAll(List<EventsCalendar> eventsCalendars);

    /**
     * seeder
     */
    void seeder();
}
