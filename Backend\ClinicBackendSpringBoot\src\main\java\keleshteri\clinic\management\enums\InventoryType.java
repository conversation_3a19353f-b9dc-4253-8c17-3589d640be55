package keleshteri.clinic.management.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum InventoryType {
    ADDITION("Addition","اضافه"),
    REMOVAL("Removal","کم"),
    RETURN("Return","عودت");

    private final String code;
    private final String label;

     InventoryType(String code, String label) {
        this.code = code;
        this.label = label;
    }

//    @JsonCreator
//    public static InventoryType decode(final String name) {
//        System.out.println("omadam");
//        return Stream.of(InventoryType.values()).filter(targetEnum -> targetEnum.name().equals(name)).findFirst().orElse(null);
//    }

    public String getName() {
        return this.name();
    }

}
