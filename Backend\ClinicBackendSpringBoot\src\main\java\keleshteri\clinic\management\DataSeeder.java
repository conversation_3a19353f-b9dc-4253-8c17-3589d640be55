package keleshteri.clinic.management;


import keleshteri.clinic.management.domain.laboratory.model.LaboratoryTest;
import keleshteri.clinic.management.domain.laboratory.service.LaboratoryTestService;
import keleshteri.clinic.management.domain.payment.service.SupplierService;
import keleshteri.clinic.management.domain.pharmacy.medicine.service.MedicineService;
import keleshteri.clinic.management.domain.product.service.CompanyService;
import keleshteri.clinic.management.domain.product.service.EventsCalendarService;
import keleshteri.clinic.management.domain.product.service.ProductGenericService;
import keleshteri.clinic.management.domain.product.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import java.net.URL;

@Component
public class DataSeeder implements CommandLineRunner {


    @Autowired
    MedicineService medicineService;

    @Autowired
    CompanyService companyService;

    @Autowired
    ProductGenericService productGenericService;

    @Autowired
    ProductService productService;

    @Autowired
    SupplierService supplierService;

    @Autowired
    EventsCalendarService eventsCalendarService;

    @Autowired
    LaboratoryTestService laboratoryTestService;

    @Override
    public void run(String... args) throws Exception {

        medicineService.seeder();
        companyService.seeder();
        productGenericService.seeder();
        productService.seeder();
        supplierService.seeder();

        laboratoryTestService.seeder();
        eventsCalendarService.seeder();

        System.out.println("Backend App is Ready");
        URL myURL = new URL("http://localhost:8080/");
        System.out.println("Please Open ");
        System.out.println(myURL);

    }
}
