package keleshteri.clinic.management.pharmacy.inventory.repository;

import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryVirtual;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.transaction.Transactional;
import java.util.List;

@Transactional
public interface InventoryVirtualRepository extends InventoryBaseRepository<InventoryVirtual> {

//    List<InventoryVirtual> findByPeriod(InventoryPeriod period);
//    /** get List Of Inventory by Period**/
//    List<InventoryVirtual> findByPeriod(InventoryPeriod period);
//    /** Get Page of Inventory by Period **/
//    Page<InventoryVirtual> findByPeriod(InventoryPeriod period, Pageable pageable);
}
