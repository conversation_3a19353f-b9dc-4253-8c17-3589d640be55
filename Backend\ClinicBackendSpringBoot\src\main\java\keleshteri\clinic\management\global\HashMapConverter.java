package keleshteri.clinic.management.global;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.io.IOException;
import java.util.Map;

@Converter
public class HashMapConverter implements AttributeConverter<Map<String, Object>, String> {

//    private ObjectMapper objectMapper;
    ObjectMapper objectMapper = new ObjectMapper();
    private final static Logger logger = LoggerFactory
            .getLogger(HashMapConverter.class);

    @Override
    public String convertToDatabaseColumn(Map<String, Object> jsonData) {

        String json = null;
        try {
            json = objectMapper.writeValueAsString(jsonData);
        } catch (final JsonProcessingException e) {
            logger.error("JSON writing error", e);
        }

        return json;
    }

    @Override
    public Map<String, Object> convertToEntityAttribute(String jsonDataAsJson) {

        Map<String, Object> jsonData = null;
        try {
            jsonData = objectMapper.readValue(jsonDataAsJson, Map.class);
        } catch (final IOException e) {
            logger.error("JSON reading error", e);
        }

        return jsonData;
    }

}