package keleshteri.clinic.management.patient_test_request;

import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.global.ControllerInterface;
import keleshteri.clinic.management.global.MainController;
import keleshteri.clinic.management.laboratory.model.LaboratoryTest;
import keleshteri.clinic.management.laboratory.service.LaboratoryTestService;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.patient.service.PatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/patient/test")
public class PatientTestRequestController extends MainController   implements ControllerInterface<PatientTestRequest,PatientTestRequestDTO> {


    private final PatientTestRequestService patientTestRequestService;
    @Autowired
    private PatientService patientService;
    @Autowired
    private LaboratoryTestService laboratoryTestService;

    @Autowired
    public PatientTestRequestController(PatientTestRequestService patientTestRequestService) {
        this.patientTestRequestService = patientTestRequestService;
    }

    /**
     *
     * Get all
     */
    @Override
    @GetMapping()
    public ResponseEntity<List<PatientTestRequest>> getAll() {
        return patientTestRequestService.get();
    }

    /**
     * getById
     */
    @Override
    @GetMapping("/{id}")
    public ResponseEntity<PatientTestRequest> getById(@PathVariable Long id) {
        return patientTestRequestService.findOne(id);
    }

    /**
     * pagination
     */
    @Override
    @GetMapping("/pagination")
    public ResponseEntity<Page<PatientTestRequest>> pagination(@RequestParam Optional<Integer> size,@RequestParam Optional<Integer> page,@RequestParam Optional<String> sort,@RequestParam Optional<String> order) {
        return patientTestRequestService.pagination(
                size,
                page,
                sort,
                this.getSortDirection(order.orElse("asc"))
        );
    }

    /**
     * create
     */
    @Override
    @PostMapping()
    public ResponseEntity<PatientTestRequest> create(@Valid @RequestBody PatientTestRequestDTO dto) {

        Patient patient =  patientService.findFirst(dto.getPatientId());
        LaboratoryTest laboratoryTest = laboratoryTestService.find(dto.getLaboratoryTestId());

        PersianDate persianDate = PersianDate.now();

        PatientTestRequest patientTestRequest = new PatientTestRequest();
        patientTestRequest.setPatient(patient);
        patientTestRequest.setLaboratoryTest(laboratoryTest);
        patientTestRequest.setRequestDatePersian(persianDate.toString());
        patientTestRequest.setRequestDate(persianDate.toGregorian());
        return patientTestRequestService.insert(patientTestRequest);
    }

    /**
     * update
     */
    @Override
    @PutMapping("/{id}")
    public ResponseEntity<PatientTestRequest> update(@PathVariable Long id,@RequestBody PatientTestRequest patientTestRequest) {
        return patientTestRequestService.edit(id,patientTestRequest);
    }

    /**
     * delete
     */
    @Override
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> delete(@PathVariable Long id) {
        return patientTestRequestService.remove(id);
    }
}
