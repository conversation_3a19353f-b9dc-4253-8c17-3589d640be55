package keleshteri.clinic.management.enums;

public enum PaymentTransactionStatus {

    /***
     *  A reversal has been canceled. For example, you won a dispute with the customer,
     *  and the funds for the transaction that was reversed have been returned to you.
     */
    CANCELED_REVERSAL("CanceledReversal","Canceled Reversal"),
    /**
     * The payment has been completed, and the funds have been added successfully to your account balance.
     */
    COMPLETED("Completed","Completed"),
    /**
     *
     */
    INCOMPLETE("incomplete","incomplete"),
    /**
     *
     */
    CANCELLED("Cancelled","Cancelled"),
    /**
     * You denied the payment.
     * This happens only if the payment was previously pending because of possible reasons described for
     * the pending_reason variable or the Fraud_Management_Filters_x variable.
     */
    DECLINED("declined","declined"),
    /**
     * The payment is pending. See pending_reason for more information.
     */
    PENDING("Pending","Pending"),
    /**
     * A payment has been accepted.
     */
    PROCESSED("Processed","Processed"),
    /**
     * This authorization has been voided.
     */
    VOIDED("Voided","Voided"),
    /**
     * This authorization has expired and cannot be captured.
     */
    EXPIRED("Expired","Expired"),

    /**
     * The payment has failed. This happens only if the payment was made from your customer’s bank account.
     */
    FAILED("Failed","Failed"),
    /**
     * You refunded the payment.
     */
    REFUNDED("Refunded","Refunded"),
    /**
     *  A payment was reversed due to a chargeback or other type of reversal.
     *  The funds have been removed from your account balance and returned to the buyer.
     *  The reason for the reversal is specified in the ReasonCode element.
     */
    REVERSED("Reversed","Reversed");



    private final String code;
    private final String label;

    PaymentTransactionStatus(String code, String label) {
        this.code = code;
        this.label = label;
    }
}
