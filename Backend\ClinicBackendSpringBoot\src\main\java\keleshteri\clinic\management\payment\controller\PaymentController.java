package keleshteri.clinic.management.payment.controller;


import keleshteri.clinic.management.enums.PaymentStatus;
import keleshteri.clinic.management.model.Payment;
import keleshteri.clinic.management.payment.request.PaymentTransactionRequest;
import keleshteri.clinic.management.payment.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v1/payments")
public class PaymentController {

    @Autowired
    private PaymentService service;

    /** All  Payment**/
    @GetMapping()
    public ResponseEntity<List<Payment>> getAll() {
        return service.pagination();
    }

    /** All Pending **/
    @GetMapping("/pending")
    public ResponseEntity<List<Payment>> getAllPending() {
        return service.pagination(PaymentStatus.Pending);
    }
    /** All Debt **/
    @GetMapping("/debt")
    public ResponseEntity<List<Payment>> getAllDebt() {
        return service.pagination(PaymentStatus.DEBT);
    }
    /** All Refunded **/
    @GetMapping("/refunded")
    public ResponseEntity<List<Payment>> getAllRefunded() {
        return service.pagination(PaymentStatus.REFUNDED);
    }


    /**
     *
     */
    @PostMapping("/{id}/transaction")
    public ResponseEntity<Payment> createPaymentTransaction(@PathVariable Long id, @Valid @RequestBody List<PaymentTransactionRequest> request) {
        //Log
        log.info("PaymentController: /api/v1/payments/{}/transaction",id);
        //find payment

        Payment payment = service.find(id);



        return ResponseEntity.ok(service.createPaymentTransaction(payment,request));
    }

    // get Payment by id rest api
    @GetMapping("/{id}")
    public ResponseEntity<Payment> getById(@PathVariable Long id) {
        return ResponseEntity.ok(service.find(id));
    }




}
