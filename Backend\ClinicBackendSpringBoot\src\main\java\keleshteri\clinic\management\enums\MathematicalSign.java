package keleshteri.clinic.management.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum MathematicalSign {
    MULTIPLICATION("Multiplication", "ضرب", "*"),
    DIVISION("Division", "تقسیم", "/");

    private final String code;
    private final String label;
    private final String sign;

    MathematicalSign(String code, String label, String sign) {
        this.code = code;
        this.label = label;
        this.sign = sign;
    }

    /**
     * Getter
     */
    @JsonValue
    public String getName() {
        return name();
    }


}
