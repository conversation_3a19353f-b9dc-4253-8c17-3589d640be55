package keleshteri.clinic.management.pharmacy.medicine;

import keleshteri.clinic.management.enums.UnitsMeasurement;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UnitsMeasurementDTO {

    private UnitsMeasurement id;

    private String name;

    private String label;

    //Constructor

    public UnitsMeasurementDTO(UnitsMeasurement id, String name, String label) {
        this.id = id;
        this.name = name;
        this.label = label;
    }
}
