package keleshteri.clinic.management.model;

import keleshteri.clinic.management.enums.CalendarType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "events_calendar")
public class EventsCalendar extends BaseEntity {

    @NotNull(message = "Please enter  title")
    @Column(nullable = false)
    private String title;

    @Column(name = "calendar_type")
    private CalendarType calendarType;

    @NotNull(message = "Please enter  location")
    @Column(nullable = false)
    private String location;

    @Column(name = "holiday")
    private boolean holiday=false;

    @Column(name = "day")
    private int day;

    @Column(name = "month")
    private int month;

    @Column(name = "year")
    private int year;

    @Column(name = "day_of_week")
    private int dayOfWeek;

    @Column(name = "day_of_year")
    private int dayOfYear;


    //Constructor


    public EventsCalendar(
            String title,
            CalendarType calendarType,
            String location,
            boolean holiday,
            int day,
            int month,
            int dayOfWeek) {
        this.title = title;
        this.calendarType = calendarType;
        this.location = location;
        this.holiday = holiday;
        this.day = day;
        this.month = month;
        this.dayOfWeek = dayOfWeek;
    }
}
