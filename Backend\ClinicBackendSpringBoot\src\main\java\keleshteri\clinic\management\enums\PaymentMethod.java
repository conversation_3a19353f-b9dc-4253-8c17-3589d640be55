package keleshteri.clinic.management.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import lombok.Getter;

import java.util.stream.Stream;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum PaymentMethod {

    CASH("Cash","نقدی"),
    CHECKS("Checks","چک"),
    SWIPE("Swipe","کارتخوان"),
    MOBILE("Mobile_Payments","اپلیکیشن موبایل"),
    INTERNET("Internet","پرداخت اینترنتی"),
    BANK("Bank","بانک");

    private final String code;
    private final String label;

    PaymentMethod(String code, String label) {
        this.code = code;
        this.label = label;
    }

    @JsonCreator
    public static PaymentMethod decode(final String name) {
        //
        String newName= name.toUpperCase();
        //
        return Stream.of(
                PaymentMethod.values()
        ).filter(targetEnum -> targetEnum.name().equals(newName)).findFirst()
                .orElseThrow(()-> new ResourceNotFoundException("PaymentMethod Not Found"));
    }

//    public String getName() {
//        return this.name();
//    }

    @JsonValue
    public String getLabelFarsi() { return this.label;}
}
