package keleshteri.clinic.management.service;

import keleshteri.clinic.management.product_generics.model.ProductGeneric;
import org.springframework.http.ResponseEntity;
import java.util.List;
import java.util.Map;

public interface ProductGenericService {
    /**
     * find by id
     */
    ProductGeneric find(Long id);

    /**
     * find by name
     */
    ProductGeneric find(String name);

    /**
     * all Products
     */
    List<ProductGeneric> all();

    /**
     * ResponseEntity Products  pagination
     */
    ResponseEntity<List<ProductGeneric>> pagination();

    /**
     * Create a New Product
     */
    ProductGeneric create(ProductGeneric product);

    /**
     * Update a Product
     */
    ProductGeneric update(Long id, ProductGeneric productDetails);

    /**
     * Delete a Product
     */
    Map<String, Boolean> delete(Long id);

    /**
     * seeder
     */
    void seeder();

}
