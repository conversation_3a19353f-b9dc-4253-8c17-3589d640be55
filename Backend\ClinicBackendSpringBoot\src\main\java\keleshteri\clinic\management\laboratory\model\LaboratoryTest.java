package keleshteri.clinic.management.laboratory.model;

import keleshteri.clinic.management.model.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@NoArgsConstructor
@Table(name = "laboratory_test")
@Entity
public class LaboratoryTest extends BaseEntity {

    @Column(name = "name", unique = true)
    private String name;

    @Column(name = "name_persian", unique = true)
    private String namePersian;


    public LaboratoryTest(String name, String namePersian) {
        this.name = name;
        this.namePersian = namePersian;
    }
}