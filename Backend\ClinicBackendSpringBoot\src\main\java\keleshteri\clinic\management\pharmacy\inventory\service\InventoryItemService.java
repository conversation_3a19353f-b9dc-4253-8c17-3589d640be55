package keleshteri.clinic.management.pharmacy.inventory.service;

import keleshteri.clinic.management.pharmacy.inventory.model.InventoryItem;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.product.model.Product;

import java.math.BigDecimal;

public interface InventoryItemService {

    //get a transaction by id
    InventoryItem find(Long id);

    //Find by id and periodId
    InventoryItem find(Long id,Long periodId);

    // Product and InventoryPeriod
    InventoryItem find(Product product, InventoryPeriod period);

    //find item or create
    InventoryItem firstOrCreate(
            Product product,
            InventoryPeriod period,
            BigDecimal purchasePrice,
            BigDecimal salePrice,
            BigDecimal salePremiumPrice,
            String expirationDate,
            Double balance
            );

    //possible duplicate
    InventoryItem firstOrCreateDuplicate(
            Product product,
            InventoryPeriod period,
            BigDecimal purchasePrice,
            BigDecimal salePrice,
            BigDecimal salePremiumPrice,
            String expirationDate,
            Double balance
    );

    //find item or create
    InventoryItem CreateOncePerPeriod(
            Product product,
            InventoryPeriod period,
            BigDecimal purchasePrice,
            BigDecimal salePrice,
            BigDecimal salePremiumPrice,
            String expirationDate,
            Double balance
    );


    //create item
    InventoryItem create(InventoryItem inventoryItem);
}
