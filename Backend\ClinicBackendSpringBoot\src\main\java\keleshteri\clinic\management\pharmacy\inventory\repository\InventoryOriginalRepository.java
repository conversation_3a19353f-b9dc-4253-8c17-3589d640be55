package keleshteri.clinic.management.pharmacy.inventory.repository;


import keleshteri.clinic.management.payment.interfaces.SourceInventoryTransaction;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryOriginal;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.transaction.Transactional;
import java.util.List;

//@Repository
//public interface InventoryOriginalRepository extends JpaRepository<InventoryOriginal,Long> {
@Transactional
public interface InventoryOriginalRepository extends InventoryBaseRepository<InventoryOriginal> {

//    /** get List Of Inventory by Period**/
//    List<InventoryOriginal> findByPeriod(InventoryPeriod period);
//    /** Get Page of Inventory by Period **/
//    Page<InventoryOriginal> findByPeriod(InventoryPeriod period, Pageable pageable);
//    List<InventoryOriginal> findByPeriod(InventoryPeriod period);

//    List<InventoryOriginal> findBySource(SourceInventoryTransaction inventoryTransaction);

//    //if has this item in Inventory
//    boolean existsByInventoryItem(InventoryItem inventoryItem);
//    //if has this item in Inventory by InventoryType
//    boolean existsByInventoryItemAndInventoryType(InventoryItem inventoryItem,InventoryType inventoryType);
//
//
//
//   ///Sum of qty this Product with type
//   @Query("SELECT SUM(c.quantity)  FROM Inventory c WHERE c.inventoryItem = :product and c.inventoryType= :type" )
//   Double sumByProductQuantity(
//           @Param("product") InventoryItem inventoryItem,
//           @Param("type") InventoryType inventoryType
//           );
//
//   //Sum of qty this Product with  group Type
//   @Query(value = "SELECT new keleshteri.clinic.management.pharmacy.virtual.InventoryBalance(SUM(c.quantity) AS balance,c.inventoryType AS inventoryType)  FROM Inventory c WHERE c.inventoryItem = :product GROUP BY c.inventoryType" )
//   List<InventoryBalance> sumQuantityGroupType(
//           @Param("product") InventoryItem inventoryItem
//   );


//   Optional <Inventory> findByProduct(Product product);

//   //sum of quantity byunits
//   //SELECT SUM(quantity),units_id,product_id FROM medicine_inventories  WHERE product_id = 1  GROUP BY units_id
//   @Query(value = "SELECT " +
//           "SUM(c.quantity) AS balance, c.units AS units,c.product AS product " +
//           " FROM Inventory c " +
//           "WHERE c.product = :product  GROUP BY c.units"
//        )
//   List<InventoryProductBalance> sumProductQuantityByGroupUnits(
//           @Param("product") Product product
//   );
//
//   //max  Period
//   @Query("SELECT MAX(c.period)  FROM Inventory c WHERE  c.inventoryType= :type" )
//   Integer MaxPeriod(@Param("type") InventoryType inventoryType);
//
//   @Query(value = "SELECT " +
//           "new keleshteri.clinic.management.pharmacy.virtual.MaxPeriodDate(MAX(c.period) AS period ,c.date AS date)  " +
//           "FROM Inventory c " +
//           "WHERE  c.inventoryType= :type and c.dateEnglish IN (SELECT MAX(dateEnglish) FROM Inventory ) " +
//           "GROUP BY c.date " )
//   Optional<MaxPeriodDate> MaxPeriodDate(@Param("type") InventoryType inventoryType);
//
//   //check if Inventory has this Product
//   @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM Inventory c WHERE c.product = :product")
//   boolean existsByProduct(@Param("product") Product product);
//
//   //check if Inventory has this Product with unitsMeasurement
//   @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM Inventory c WHERE c.product = :product and c.units= :units")
//   boolean existsByProductWithUnits(
//           @Param("product") Product product,
//           @Param("units")UnitsMeasurement unitsMeasurement
//           );
//
//   //check if Inventory has this Product with type any other unitsMeasurement
//   @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM Inventory c WHERE c.product = :product and c.inventoryType= :type")
//   boolean existsByProductWithTransactionType(
//           @Param("product") Product product,
//           @Param("type") InventoryType inventoryType
//   );
//
//   //check if Inventory has this Product with unitsMeasurement And type
//   @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM Inventory c WHERE c.product = :product and c.inventoryType= :type and c.units= :units")
//   boolean existsByProductWithUnitsWithTransactionType(
//           @Param("product") Product product,
//           @Param("type") InventoryType inventoryType,
//           @Param("units")UnitsMeasurement unitsMeasurement
//   );
//
//   //check if Inventory has this Product with any Balance  with unitsMeasurement And type
//   //Without remove out
//   @Query("SELECT CASE WHEN SUM(c.quantity) > 0 THEN true ELSE false END FROM Inventory c WHERE c.product = :product and c.inventoryType= :type and c.units= :units")
//   boolean existsByProductWithBalanceUnitsWithTransactionType(
//           @Param("product") Product product,
//           @Param("type") InventoryType inventoryType,
//           @Param("units")UnitsMeasurement unitsMeasurement
//   );
//

//

//
//   ///Sum of qty this Product  group by Type and unitsMeasurement
//   @Query(value = "SELECT c.units AS units,c.inventoryType AS inventoryType , SUM(c.quantity) AS balance  FROM Inventory c WHERE c.product = :product  GROUP BY c.units, c.inventoryType" )
//   List<InventoryProductBalance> sumByProductQuantityGroupTypeUnits(
//           @Param("product") Product product
//   );



   // Note that only HQL Inquire about , Cannot add nativeQuery = true
//   @Query(value = "select new keleshteri.clinic.management.pharmacy.model.InventoryBalance(a.ac.inventoryType AS inventoryType , SUM(c.quantity) AS balance)  FROM Inventory c WHERE c.product = :product and c.units= :units GROUP BY c.inventoryType")
//   List<InventoryBalance> sumByProductQuantity2(@Param("product")Product product,
//                                                @Param("units")UnitsMeasurement unitsMeasurement);

//   Query(value = "SELECT v.postid AS postid,COUNT(v.postid) AS viewcnt
//           FROM viewposttable AS v GROUP BY v.postid
//           ORDER BY COUNT(v.postid) DESC ", nativeQuery = true)
//   List<PostidandviewcntWrapperclass> findPostidAndViewCntByTime();
}
