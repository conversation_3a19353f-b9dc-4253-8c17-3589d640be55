package keleshteri.clinic.management.pharmacy.medicine.controller;



import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.pharmacy.medicine.UnitsMeasurementDTO;
import keleshteri.clinic.management.pharmacy.medicine.enums.MedicineType;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.pharmacy.medicine.service.MedicineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@RestController
@RequestMapping("/api/v1/medicines")
public class MedicineController {


    private final MedicineService medicineService;

    @Autowired
    public MedicineController(MedicineService medicineService) {
        this.medicineService = medicineService;
    }

    /**
     *
     */
    /**
     * Get All Medicine pagination
     */
    @GetMapping
    public ResponseEntity<List<Medicine>> getAll(){
        return medicineService.pagination();
    }

    /**
     * Get All Types Medicine
     */
    @GetMapping("/types")
    public ResponseEntity<List<Map<String,Object>>>getTypeAll(){

        List<Map<String,Object>> types= new ArrayList<>();

        Arrays.stream(MedicineType.values()).forEach(T->{
            types.add(T.getList());
        });

        return ResponseEntity.ok(types);
    }
    /**
     * Get All UnitsMeasurement
     */
    @GetMapping("/units")
    public ResponseEntity<List<Map<String,Object>>>getUnitsMeasurementsAll(){

        List<Map<String,Object>> units= new ArrayList<>();

        Arrays.stream(UnitsMeasurement.values()).forEach(U->{
            units.add(U.getList());
        });

        return ResponseEntity.ok(units);
    }

    /**
     *
     */
    @GetMapping("/{id}/units")
    public ResponseEntity<Set<UnitsMeasurement>>getMedicineUnitsMeasurements(@PathVariable Long id){
        //find medicine
        Medicine medicine = medicineService.find(id);
        return ResponseEntity.ok(medicine.getUnits());
    }

    /**
     *
     */
    @GetMapping("/{id}/unit")
    public ResponseEntity<List<UnitsMeasurementDTO>>getMedicineUnitsMeasurement(@PathVariable Long id){
        //find medicine
        Medicine medicine = medicineService.find(id);

        UnitsMeasurementDTO  unit = new UnitsMeasurementDTO(medicine.getUnit(),medicine.getUnit().getName(),medicine.getUnitLabel());


        List<UnitsMeasurementDTO> unitsList= new ArrayList<>();
        unitsList.add(unit);
        return ResponseEntity.ok(unitsList);
    }
    /**
     *
     */
    @GetMapping("/{id}/unit/label")
    public ResponseEntity<String>getMedicineUnitsMeasurementLabel(@PathVariable Long id){
        //find medicine
        Medicine medicine = medicineService.find(id);

        UnitsMeasurementDTO  unit = new UnitsMeasurementDTO(medicine.getUnit(),medicine.getUnit().getName(),medicine.getUnitLabel());

        return ResponseEntity.ok(unit.getLabel());
    }
    /**
     * Get a Medicine  BY Id
     */
    @GetMapping("/{id}")
    public ResponseEntity<Medicine> getById(@PathVariable Long id){
        return ResponseEntity.ok(medicineService.find(id));
    }

    /**
     * Create a Medicine
     */
    @PostMapping
    public ResponseEntity<Medicine> create(@Valid @RequestBody Medicine medicine){
        return ResponseEntity.ok(medicineService.create(medicine));
    }

    /**
     * Update
     */
    @PutMapping("/{id}")
    public ResponseEntity<Medicine> update(@PathVariable Long id, @RequestBody Medicine medicineDetails){
        return ResponseEntity.ok(medicineService.update(id,medicineDetails));
    }

    /**
     * Delete
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> delete(@PathVariable Long id){
        return ResponseEntity.ok(medicineService.delete(id));
    }
}
