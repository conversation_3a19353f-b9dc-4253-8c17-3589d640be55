package keleshteri.clinic.management.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.enums.PaymentStatus;
import keleshteri.clinic.management.pharmacy.sale.model.Sale;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "payments")
public class Payment extends BaseEntity {



    //paymentDate
    @NotNull(message = "Please enter date")
    @Column(name = "payment_date_persian")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "payment date Persian"
    )
    private  String paymentDatePersian;

    //paymentDate
    @Column(name = "payment_date")
    private LocalDate paymentDate;

//    //PaymentMethod
//    @NotNull(message = "Please enter  payment Method")
//    @Column(name = "payment_type")
//    private PaymentMethod paymentMethod;

    //PaymentStatus
    @NotNull(message = "Please enter  Payment Status")
    @Column(name = "payment_status")
    private PaymentStatus paymentStatus;

    //Sale
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "sale_id",nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
//    @JsonIgnore
    private Sale sale;

    //amount
    @NotNull(message = "Please enter amount")
    @Column(name = "amount")
    private BigDecimal amount;

    //if has debt
    @Column(name = "debt_amount")
    private BigDecimal debtAmount;



    //***
    //payment
    @OneToMany(mappedBy = "payment", cascade = CascadeType.ALL)
    private Set<PaymentTransaction> transactions = new HashSet<>();


    /***
     * Getter Setter
     */
    public void setPaymentDatePersian(String paymentDatePersian) {
        this.paymentDate= PersianDate.parse(paymentDatePersian).toGregorian();
        this.paymentDatePersian = paymentDatePersian;
    }

    public BigDecimal getSumAmountTransactions() {
        BigDecimal sum =  transactions.stream()
                .map(t->t.getAmount())
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        return sum;
    }

    /***
     * Constructor
     */
    public Payment(
            String paymentDatePersian,
//            PaymentMethod paymentMethod,
            Sale sale,
            BigDecimal amount) {
        this.paymentDatePersian = paymentDatePersian;
        this.paymentDate= PersianDate.parse(paymentDatePersian).toGregorian();
//        this.paymentMethod = paymentMethod;
        this.paymentStatus=PaymentStatus.Pending;
        this.sale = sale;
        this.amount = amount;
    }

    public Payment(
            String paymentDatePersian,
//            PaymentMethod paymentMethod,
            PaymentStatus paymentStatus,
            Sale sale,
            BigDecimal amount) {
        this.paymentDatePersian = paymentDatePersian;
        this.paymentDate= PersianDate.parse(paymentDatePersian).toGregorian();
//        this.paymentMethod = paymentMethod;
        this.paymentStatus=paymentStatus;
        this.sale = sale;
        this.amount = amount;
    }


}
