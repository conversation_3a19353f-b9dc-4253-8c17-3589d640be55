package keleshteri.clinic.management.pharmacy.medicine.repository;

import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MedicineRepository extends JpaRepository<Medicine,Long> {
    //Find by name medicine
    Optional<Medicine> findByName(String name);

    //Get max Great number of code
    @Query(value = "SELECT max(code) FROM Medicine")
    int getMaxCode();
}
