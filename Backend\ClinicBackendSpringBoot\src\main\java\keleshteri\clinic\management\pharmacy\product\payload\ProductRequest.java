package keleshteri.clinic.management.pharmacy.product.payload;

import keleshteri.clinic.management.pharmacy.medicine.enums.MedicineType;
import keleshteri.clinic.management.model.ProductUnitsConversion;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class ProductRequest {

    /** Code **/

    private Integer code;

    /** BarCode **/

    private String barCode;

    /** Name **/
    @NotNull(message = "نام کالا را وارد کنید")
    private String name;

    /** Generic Name **/
    @NotNull(message = "نام ژنریک")
    private String genericName;

    /** Medicine **/
    @NotNull(message = "دارو")
    private Long medicine;

    /** Company **/
    @NotNull(message = "شرکت")
    private Long companyId;

    /** MedicineType **/
    @NotNull(message = "نوع دارو")
    private MedicineType type;

    /** Product UnitsConversion **/
    private List<ProductUnitsConversion> unitsConversion;

}
