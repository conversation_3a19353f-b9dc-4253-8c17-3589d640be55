package keleshteri.clinic.management.patient.helper;

import keleshteri.clinic.management.patient.virtual.PrescriptionPeriod;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

public class PrescriptionPeriodExportExcelHelper {
    public static String TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    static String[] HEADERs = {"شمار پرونده", "بیمار", "دارو","دوز","واحد اندازه گیری","دوره درمان","تاریخ تجویز" };
    static String SHEET = "PeriodPrescriptions";

    public static ByteArrayInputStream prescriptionsToExcel(List<PrescriptionPeriod> prescriptionList) {

        try (Workbook workbook = new XSSFWorkbook(); ByteArrayOutputStream out = new ByteArrayOutputStream();) {
            Sheet sheet = workbook.createSheet(SHEET);

            // Header
            Row headerRow = sheet.createRow(0);

            for (int col = 0; col < HEADERs.length; col++) {
                Cell cell = headerRow.createCell(col);
                cell.setCellValue(HEADERs[col]);
            }

            int rowIdx = 1;
            for (PrescriptionPeriod prescription : prescriptionList) {
                Row row = sheet.createRow(rowIdx++);

                row.createCell(0).setCellValue(prescription.getPrescription().getPatient().getFileNumber());
                row.createCell(1).setCellValue(prescription.getPrescription().getPatient().getName()+" "+prescription.getPrescription().getPatient().getLastName());
                row.createCell(2).setCellValue(prescription.getMedicine().getName());
                row.createCell(3).setCellValue(prescription.getDosePeriod());
                row.createCell(4).setCellValue(prescription.getDoseUnits().getLabel());
                row.createCell(5).setCellValue(prescription.getPeriodDays());
                row.createCell(6).setCellValue(prescription.getStartDatePersian());
            }

            workbook.write(out);
            return new ByteArrayInputStream(out.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("fail to Export data to Excel file: " + e.getMessage());
        }
    }
}
