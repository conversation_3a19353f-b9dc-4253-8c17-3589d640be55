package keleshteri.clinic.management.pharmacy.sale.service.impl;

import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.exception.InvalidSaleException;
import keleshteri.clinic.management.exception.InventoryException;
import keleshteri.clinic.management.exception.ResourceNotFoundException;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.patient.service.PatientService;
import keleshteri.clinic.management.enums.PaymentStatus;
import keleshteri.clinic.management.enums.PaymentTransactionStatus;
import keleshteri.clinic.management.enums.PaymentTransactionType;
import keleshteri.clinic.management.payment.interfaces.SourceClient;
import keleshteri.clinic.management.model.Payment;
import keleshteri.clinic.management.model.PaymentTransaction;
import keleshteri.clinic.management.payment.repository.PaymentRepository;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.pharmacy.sale.payload.SaleDateUpdateRequest;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.service.ProductService;
import keleshteri.clinic.management.pharmacy.sale.payload.SaleItemRequest;
import keleshteri.clinic.management.pharmacy.sale.payload.SaleRequest;
import keleshteri.clinic.management.payment.service.CustomerService;
import keleshteri.clinic.management.payment.service.PaymentService;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryItemService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryPeriodService;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryOriginalService;
import keleshteri.clinic.management.pharmacy.sale.model.Sale;
import keleshteri.clinic.management.pharmacy.sale.model.SaleItem;
import keleshteri.clinic.management.pharmacy.sale.repository.SaleRepository;
import keleshteri.clinic.management.pharmacy.sale.service.SaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class SaleServiceImpl implements SaleService {

    @Autowired
    private SaleRepository repository;

    @Autowired
    private InventoryOriginalService inventoryService;

    @Autowired
    private InventoryPeriodService periodService;

    @Autowired
    private InventoryItemService itemService;

    @Autowired
    private ProductService productService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private PatientService patientService;


    public ResponseEntity<List<Sale>> pagination(InventoryPeriod period){
        List<Sale> sales = repository.findByPeriod(period);


        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "transactions 0-"+sales.size() + "/" + sales.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(sales);
    }

    @Override
    public ResponseEntity<List<Sale>> pagination(InventoryPeriod period, Patient patient) {
        /** sale find by Period**/
        List<Sale> salesAll =  repository.findByPeriod(period);

        List<Sale> sales = salesAll.stream().filter(sale -> {
           return sale.getClient().equals(patient);
        }).collect(Collectors.toList());


        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "transactions 0-"+sales.size() + "/" + sales.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(sales);
    }

    public ResponseEntity<List<SaleItem>> paginationSaleItem(InventoryPeriod period, Patient patient) {
        /** sale find by Period**/
        List<Sale> salesAll =  repository.findByPeriod(period);

        List<Sale> sales = salesAll.stream().filter(sale -> {
            return sale.getClient().equals(patient);
        }).collect(Collectors.toList());

        //get all items
        List<SaleItem> items= new ArrayList<>();
        sales.stream().forEach(sale -> {
            items.addAll(sale.getItems());
        });
        System.out.println(items.size());

//        List<Map<String,Double>> group = items
//                .stream()
//                .collect(Collectors.groupingBy(item -> {
//                      item.getProduct().getName(),Collectors.summingDouble(item.getQuantity().doubleValue())
//                }));
//        Collectors.groupingBy(
//                pre -> pre.getMedicine().getName(), Collectors.summingDouble(pre -> pre.getDosePeriod())
//        )
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "items 0-"+items.size() + "/" + items.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(items);
    }

    @Override
    public Sale refund(Long saleId) {
        Sale sale= repository.findById(saleId)
                .orElseThrow(()-> new ResourceNotFoundException("sale not found"));
        if(sale.isReFound()==false){
            //sale set to reFound
            sale.setReFound(true);
            //refund payment
            Payment payment=  sale.getPayments();
            payment.setPaymentStatus(PaymentStatus.REFUNDED);

            paymentRepository.save(payment);

            //return items to Inventory
            /****
             * create Inventory
             ****/
            inventoryService.returnItem(sale);

            Sale SaveSale= repository.save(sale);
            return SaveSale;
        }
        return sale;
    }

    @Override
    public Sale UpdateDate(Long saleId,SaleDateUpdateRequest request) {
        Sale sale= repository.findById(saleId)
                .orElseThrow(()-> new ResourceNotFoundException("sale not found"));
        /** update find Medicine **/
        sale.setInvoiceDatePersian(request.getInvoiceDatePersian());

        /** save **/
        Sale saveSale= repository.save(sale);

        return saveSale;
    }

    /**
     * Deletes a sale and returns its items to inventory
     * 
     * Process:
     * 1. Find the sale by ID
     * 2. Check if it's not already refunded
     * 3. Return items to inventory
     * 4. Delete the sale record and its related entities
     * 
     * Note: This method is transactional to ensure both inventory return
     * and sale deletion happen together or not at all
     * 
     * @param saleId ID of the sale to delete
     * @throws ResourceNotFoundException if sale not found
     * @throws InvalidSaleException if sale is already refunded
     */
    @Override
    @Transactional
    public void delete(Long saleId) {
        log.info("Starting deletion process for sale ID: {}", saleId);
        
        Sale sale = repository.findById(saleId)
                .orElseThrow(() -> new ResourceNotFoundException("Sale not found with id: " + saleId));

        log.info("Found sale to delete: {} from date: {}", saleId, sale.getInvoiceDatePersian());

        // If the sale is already refunded, we can't delete it
        if (sale.isReFound()) {
            log.error("Cannot delete sale {} - already refunded", saleId);
            throw new InvalidSaleException("Cannot delete a refunded sale");
        }

        try {
            // Return items to inventory using the existing returnItem method
            log.info("Returning items to inventory for sale: {}", saleId);
            inventoryService.returnItem(sale);

            // First handle payment and its transactions
            log.info("Handling payment deletion for sale: {}", saleId);
            if (sale.getPayments() != null) {
                Payment payment = sale.getPayments();
                if (payment.getTransactions() != null) {
                    payment.getTransactions().clear();
                }
                paymentRepository.delete(payment);
                sale.setPayments(null);
            }

            // Then handle sale items
            log.info("Clearing sale items for sale: {}", saleId);
            if (sale.getItems() != null) {
                sale.getItems().clear();
            }

            // Finally delete the sale using native query
            log.info("Deleting sale record: {}", saleId);
            repository.deleteSaleById(saleId);
            
            log.info("Successfully deleted sale: {} and all related records", saleId);
        } catch (Exception e) {
            log.error("Error during sale deletion process: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    public ResponseEntity<Page<Sale>> pagination(
            Optional<Integer> size,
            Optional<Integer> page,
            Optional<String> sortBy,
            boolean desc
    ) {

        Page<Sale> sales = repository.findAll(
                PageRequest.of(
                        page.orElse(0),
                        size.orElse(5),
                        desc==true?Sort.Direction.DESC:Sort.Direction.ASC,
                        sortBy.orElse("id")
                )
        );

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "sales 0-"+sales.getTotalPages() + "/" + sales.getTotalPages());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(sales);
    }

    @Override
    public ResponseEntity<Page<Sale>> pagination(
            Optional<Integer> size,
            Optional<Integer> page,
            Optional<String> sortBy,
            boolean desc,
            Optional<String> start_date,
            Optional<String> end_date) {
        LocalDate start = PersianDate.parse(start_date.orElse("")).toGregorian();
        LocalDate end = PersianDate.parse(end_date.orElse("")).toGregorian();
        Page<Sale> sales = repository.findByInvoiceDateBetween(
                start,
                end,
                PageRequest.of(
                        page.orElse(0),
                        size.orElse(5),
                        desc==true?Sort.Direction.DESC:Sort.Direction.ASC,
                        sortBy.orElse("id")
                )
        );

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "sales 0-"+sales.getTotalPages() + "/" + sales.getTotalPages());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(sales);
    }

    @Override
    public ResponseEntity<Page<Sale>> pagination(
            Optional<Integer> size,
            Optional<Integer> page,
            Optional<String> sortBy,
            boolean desc,
            Optional<String> start_date,
            Optional<String> end_date,
            Long clientId,
            String clientType) {
        //start_date
        LocalDate start = PersianDate.parse(start_date.orElse("")).toGregorian();
        //end_date
        LocalDate end = PersianDate.parse(end_date.orElse("")).toGregorian();
        //find client

        log.info("check who is the client  null: {}",clientType == null);
        SourceClient sourceClient ;
        if((clientId != null)&&(clientType != null) && clientType.contains("Customer")){
            sourceClient= customerService.find(clientId);
            log.info("client is a Customer and name {}",customerService.find(clientId).getName());
        }else if((clientId != null)&&(clientType != null) && clientType.contains("Patient")) {
            log.info("client is a Patient and name {}",patientService.findFirst(clientId).getName());
            sourceClient= patientService.findFirst(clientId);
        }else {
            sourceClient=null;
            log.error("no Client ");
        }
        /**
         *   sales =
         */
        //sales
//        Page<Sale> sales  = repository.findByInvoiceDateBetween(
//                start,
//                end,
//                PageRequest.of(
//                        page.orElse(0),
//                        size.orElse(5),
//                        desc==true?Sort.Direction.DESC:Sort.Direction.ASC,
//                        sortBy.orElse("id")
//                )
//        );
        List<Sale> sales  = repository.findByInvoiceDateBetween(
                start,
                end);

        Pageable pageableSales = PageRequest.of(
                        page.orElse(0),
                        size.orElse(5),
                        desc==true?Sort.Direction.DESC:Sort.Direction.ASC,
                        sortBy.orElse("id")
                );
        //salesPage
        Page<Sale> salesPage = new PageImpl<Sale>(sales,pageableSales,sales.size());


        //if
        if(sourceClient !=null){
            sales.forEach(mm->{
                log.info("data {}",mm.getClient());
            });
            List<Sale> FilteredDataSale = sales.stream().filter(sale -> sale.getClient().equals(sourceClient)).collect(Collectors.toList());
            log.info("count record {}",FilteredDataSale.size());
            salesPage = new PageImpl<Sale>(FilteredDataSale,pageableSales,sales.size());
        }


        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "sales 0-"+salesPage.getTotalPages() + "/" + salesPage.getTotalPages());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(salesPage);
    }


    @Override
    public ResponseEntity<List<Sale>> pagination() {
        List<Sale> sales = repository.findAll();

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Content-Range", "transactions 0-"+sales.size() + "/" + sales.size());

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(sales);
    }

    /***
     *
     * Create Sales order
     *
     * @throws InvalidSaleException
     */
    @Override
    @Transactional//(rollbackFor = {Exception.class, InvalidSaleException.class})
    public Sale Create(SaleRequest request) throws InvalidSaleException{
        log.info("start to save new sale");

        /***
         * find InventoryPeriod
         * Get the active Period
         ***/
        log.info("find InventoryPeriod");
        if(request.getInventoryPeriod()==null){
            log.error("request is empty for InventoryPeriod");
            throw new InvalidSaleException("دور را وارد کنید");
        }
        InventoryPeriod period=periodService.find(request.getInventoryPeriod());
        log.info("InventoryPeriod is {}",period.getName());


        /**
         * find the source Customer-Patient
         */
        log.info("check who is the client");
        SourceClient sourceClient ;
        if(request.getSourceClientType().contains("Customer")){
            sourceClient= customerService.find(request.getSourceClientId());
            log.info("client is a Customer and name {}",customerService.find(request.getSourceClientId()).getName());
        }else if(request.getSourceClientType().contains("Patient")) {
            log.info("client is a Patient and name {}",patientService.findFirst(request.getSourceClientId()).getName());
            sourceClient= patientService.findFirst(request.getSourceClientId());
        }else {
            sourceClient=null;
            log.error("no Client ");
        }
         //check items more than 0
        log.info("check items more than 0 ");
        //check if items is 0
        if(request.getItems().size()==0){
            log.error("items is 0");
            throw new InventoryException("0 محصول مجاز نیست");
        }
        log.info("save sale with out items");
        //create Sale- Order
        Sale sale = repository.save(
                new Sale(
                        request.getInvoiceDatePersian(),
                        sourceClient,
                        period,
                        request.getComments(),
                        request.getDiscount()
                )
        );
        log.info("sale saved id is :{}",sale.getId());
        log.info("add items to sale {} items",request.getItems().size());
        //create OrderItems
        List<SaleItem> saleItems = new ArrayList<>();
        List<SaleItemRequest> itemRequest = request.getItems();

        /******
         *convert itemRequest to order item
         *  add
         ******/
        log.info("foreach itemRequest start");
        itemRequest.forEach(itemList->{
            /**
             * find productVariant by id
             */
            Product product=productService.find(itemList.getProductId());
            log.info("product is {}",product.getName());

            /**
             * check if item request has duplicate item
             */
            if(saleItems.stream().filter(ol->ol.getProduct().equals(product)).count()>0){
                throw new InventoryException("یک محصول را بیشتر از یک بار وارد کردید");
            }

            /****
             * add item in SaleItem orderItems
             */
            saleItems.add(
                    new SaleItem(
                            sale,//order
                            product,//ProductVariant ,
                            itemList.getQuantity(),//quantity,
                            itemList.getIsConverted(), //IsConverted
                            itemList.getQuantityConverted(),  //quantityConverted,
                            itemList.getUnitsConverted(),//unitsConverted
                            itemList.getUnitPrice(), //unitPrice,
                            itemList.getDiscount() //discount
                    )
            );
        });
        //ADD OrderItem
        sale.getItems().addAll(saleItems);
        //save
        repository.save(sale);

        /****
         ** Payment
         ****/
        Payment payment = new Payment(
                sale.getInvoiceDatePersian(),//paymentDatePersian
                PaymentStatus.Pending,  //paymentStatus
                sale,//sale
                sale.getTotalWDiscount()//amount
        );
        //save
        paymentService.create(payment);

        //create PaymentTransaction
        List<PaymentTransaction> transactionList = new ArrayList<>();

        request.getTransactions().forEach(transaction->{
            transactionList.add(
                    new PaymentTransaction(
                            payment,//payment,
                            PaymentTransactionType.PAY_SELL,//paymentTransactionType,
                            PaymentTransactionStatus.COMPLETED,//paymentTransactionStatus,
                            transaction.getPaymentMethod(),//paymentMethod,
                            transaction.getAmount()//amount
                    )
            );

        });


        //check amount of Transaction and payment
        BigDecimal total = transactionList.stream().map(t->t.getAmount())
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal different= payment.getAmount().subtract(total);

        //check
        if(different.doubleValue()>0){
            payment.setDebtAmount(different);
            payment.setPaymentStatus(PaymentStatus.DEBT);
        }else if(different.doubleValue()<0){
            payment.setDebtAmount(different);
            payment.setPaymentStatus(PaymentStatus.PAID);
        }
        else if(different.doubleValue()==0) {
            payment.setPaymentStatus(PaymentStatus.PAID);
        }

        payment.getTransactions().addAll(transactionList);

        Payment savePayment =paymentRepository.save(payment);

        /****
         * create Inventory
         ****/
        inventoryService.removeItem(sale);

        return sale;
    }
}
