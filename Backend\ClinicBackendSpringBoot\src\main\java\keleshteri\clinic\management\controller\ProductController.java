package keleshteri.clinic.management.controller;


import keleshteri.clinic.management.product_generics.model.ProductGeneric;
import keleshteri.clinic.management.pharmacy.inventory.model.InventoryPeriod;
import keleshteri.clinic.management.pharmacy.inventory.service.InventoryPeriodService;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.pharmacy.medicine.service.MedicineService;
import keleshteri.clinic.management.model.Company;
import keleshteri.clinic.management.product.model.Product;
import keleshteri.clinic.management.pharmacy.product.payload.ProductItemInventory;
import keleshteri.clinic.management.pharmacy.product.payload.ProductRequest;
import keleshteri.clinic.management.service.CompanyService;
import keleshteri.clinic.management.service.ProductGenericService;
import keleshteri.clinic.management.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.*;


@RestController
@RequestMapping("/api/v1/products")
public class ProductController {


    private final ProductService productService;

    @Autowired
    private InventoryPeriodService inventoryPeriodService;

    @Autowired
    private ProductGenericService productGenericService;

    @Autowired
    private MedicineService medicineService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    public ProductController(ProductService productService) {
        this.productService = productService;
    }

    /**
     * Get All Products pagination
     */
    @GetMapping
    public ResponseEntity<List<Product>> getAll(){
        return productService.pagination();
    }

    /**
     * Get a Product  BY Id
     */
    @GetMapping("/{id}")
    public ResponseEntity<Product> getById(@PathVariable Long id){
        return ResponseEntity.ok(productService.find(id));
    }

    /**
     *
     * @param periodId
     * @return
     */
    @GetMapping("/items/period/{periodId}")
    public ResponseEntity<List<Product>> getProductByPeriodAll(@PathVariable Long periodId) {
        InventoryPeriod inventoryPeriod = inventoryPeriodService.find(periodId);
        return ResponseEntity.ok(productService.productItemsByPeriod(inventoryPeriod));
    }

    @GetMapping("/items/inventory/map")
    public  ResponseEntity<List<Map<String,Object>>> getProductItemsInventoryAllMap() {
        return productService.itemsMap();
    }

    @GetMapping("/items/inventory")
    public  List<ProductItemInventory> getProductItemsInventoryAll() {
        return productService.items();
    }

    /** ProductItemInventory**/
    @GetMapping("/items/inventory/period/{periodId}")
    public  List<ProductItemInventory> getProductItemsInventoryByPeriodAll(@PathVariable Long periodId) {

        InventoryPeriod inventoryPeriod = inventoryPeriodService.find(periodId);
        List<ProductItemInventory> productItemInventoryList=productService.productItemsInventoryByPeriod(inventoryPeriod);
        System.out.println(productItemInventoryList.size());
        return productItemInventoryList;
    }

    /** ProductItemInventory**/
    @GetMapping("/items/inventory/virtual/period/{periodId}")
    public  List<ProductItemInventory> getProductItemsInventoryVirtualByPeriodAll(@PathVariable Long periodId) {
        InventoryPeriod inventoryPeriod = inventoryPeriodService.find(periodId);
        return productService.productItemsInventoryVirtualByPeriod(inventoryPeriod);
    }


    /**
     * Create a Product
     */
    @PostMapping
    public ResponseEntity<Product> create(@Valid @RequestBody ProductRequest request){
        //
        Medicine medicine = medicineService.find(request.getMedicine());
        //
        Company company = companyService.find(request.getCompanyId());
        //
        ProductGeneric productGeneric= productGenericService.find(request.getGenericName());

        Product product= new Product(
                request.getCode(),//code,
                productGeneric,//genericName,
                request.getName(),//name
                company,//company
                medicine,//medicine
                request.getType() //medicineType
               );


        return ResponseEntity.ok(productService.create(product));
    }

    /**
     * Update
     */
    @PutMapping("/{id}")
    public ResponseEntity<Product> update(@PathVariable Long id, @RequestBody Product productDetails){
        return ResponseEntity.ok(productService.update(id,productDetails));
    }

    /**
     * Delete
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> delete(@PathVariable Long id){
        return ResponseEntity.ok(productService.delete(id));
    }

}
