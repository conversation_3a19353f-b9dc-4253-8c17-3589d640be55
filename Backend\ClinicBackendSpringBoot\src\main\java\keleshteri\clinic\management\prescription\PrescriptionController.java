package keleshteri.clinic.management.prescription;


import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.calendar.IslamicDateCalendar;
import keleshteri.clinic.management.calendar.PersianDateCalendar;
import keleshteri.clinic.management.enums.CalendarType;
import keleshteri.clinic.management.exception.InvalidPrescriptionException;
import keleshteri.clinic.management.model.EventsCalendar;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.patient.payload.ExpireDateCreatetorRequest;
import keleshteri.clinic.management.patient.payload.PrescriptionRequest;
import keleshteri.clinic.management.patient.service.PatientService;
import keleshteri.clinic.management.patient.virtual.PrescriptionPeriod;
import keleshteri.clinic.management.patient.virtual.PrescriptionPeriodSum;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.pharmacy.medicine.service.MedicineService;
import keleshteri.clinic.management.prescription.payload.PrescriptionPaginationResponse;
import keleshteri.clinic.management.service.EventsCalendarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.time.DayOfWeek;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@EnableTransactionManagement
@RequestMapping("/api/v1/patient/prescriptions")
public class PrescriptionController {


    private final PrescriptionService service;

    @Autowired
    private EventsCalendarService eventsCalendarService;

    @Autowired
    private PatientService patientService;

    @Autowired
    private MedicineService medicineService;

    @Autowired
    public PrescriptionController(PrescriptionService service) {
        this.service = service;
    }



    /***
     * get LIST Prescription of patient
     */
    @GetMapping("/patient/{patientId}")
    public ResponseEntity<List<Prescription>>getAllByPatient(@PathVariable Long patientId){
        Patient patient= patientService.findFirst(patientId);
        return service.paginationByPatient(patient);
    }

    /***
     * get last Prescription of patient
     */
    @GetMapping("/patient/last/{patientId}")
    public ResponseEntity<Prescription>getLastByPatient(@PathVariable Long patientId){
        Patient patient= patientService.findFirst(patientId);
        return service.LastPrescriptionByPatient(patient);
    }

    /**
     *
     */
    @GetMapping("/download")
    public ResponseEntity<Resource> getFile() {
        String filename = "prescriptions.xlsx";
        InputStreamResource file = new InputStreamResource(service.excelFileGenerate());

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(file);
    }


    //get
    @PostMapping("/expire_date")
    public ResponseEntity<Map<String,Object>> ExpireDate(@Valid @RequestBody ExpireDateCreatetorRequest request){

        //get date start
        PersianDate start_date = PersianDate.parse(request.getStartDatePersian());
        PersianDate  expireDate= start_date.plusDays(request.getTreatmentPeriodDay());
        //
        PersianDateCalendar persianDate= new PersianDateCalendar(expireDate.getYear(),expireDate.getMonthValue(),expireDate.getDayOfMonth());

        IslamicDateCalendar islamicDate = new IslamicDateCalendar(persianDate.toJdn());
        System.out.println(islamicDate.getYear()+"/"+islamicDate.getMonth()+"/"+islamicDate.getDayOfMonth());
        //Events
        List<EventsCalendar> events= new ArrayList<>();
        //Events Hijri
        List<EventsCalendar> eventsHijri=  eventsCalendarService.find(CalendarType.HIJRI, islamicDate.getMonth(), islamicDate.getDayOfMonth());
        //Events Persian
        List<EventsCalendar> eventsPersian=  eventsCalendarService.find(CalendarType.PERSIAN,expireDate.getMonthValue(),expireDate.getDayOfMonth());

        System.out.println(expireDate.getDayOfWeek().getValue());
//        System.out.println(expireDate.getDayOfWeek().equals(DayOfWeek.FRIDAY));


        if(expireDate.getDayOfWeek().equals(DayOfWeek.FRIDAY)){
            eventsPersian.add(new EventsCalendar(
                    "روز جمعه هست.",
                    CalendarType.PERSIAN,
                    "iran",
                    true,
                    expireDate.getDayOfMonth(),
                    expireDate.getMonthValue(),
                    expireDate.getDayOfWeek().getValue()

            ) );
        }

        events.addAll(eventsPersian);
        events.addAll(eventsHijri);

        //create string array out put
        Map<String,Object> DateList= new HashMap<>();
        DateList.put("date",expireDate.toString());
        DateList.put("islamicDate",islamicDate.getYear()+"-"+islamicDate.getMonth()+"-"+islamicDate.getDayOfMonth());
        DateList.put("year",String.valueOf(expireDate.getYear()));
        DateList.put("month",String.valueOf(expireDate.getMonth()));
        DateList.put("monthValue",String.valueOf(expireDate.getMonthValue()));
        DateList.put("dayOfMonth",String.valueOf(expireDate.getMonth()));
        DateList.put("dayOfWeek",String.valueOf(expireDate.getDayOfWeek()));
        DateList.put("islamicYear", String.valueOf(islamicDate.getYear()));
        DateList.put("islamicMonth", String.valueOf(islamicDate.getMonth()));
        DateList.put("islamicDay", String.valueOf(islamicDate.getDayOfMonth()));
        DateList.put("events",events);


        return ResponseEntity.ok(DateList);
    }

    //get all
    @GetMapping
    public ResponseEntity<PrescriptionPaginationResponse> getAll(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String fileNumber,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String medicineId
    ) {
        return service.pagination(page, size, sortBy, sortDir, fileNumber, startDate, medicineId);
    }

    @GetMapping("/today/expired")
    public ResponseEntity<List<Prescription>> ExpiredToDayListPagination() {
        return service.ExpiredToDayListPagination();
    }
    /**
     * get Prescriptions of today
     */
    @GetMapping("/today/period")
    public ResponseEntity<List<PrescriptionPeriod>> getAllPrescriptionPeriodToday() {
        return service.paginationPrescriptionPeriodToday();
    }

    /**
     * get Prescriptions of today delivery
     */
    @GetMapping("/today/period/delivery")
    public ResponseEntity<List<PrescriptionPeriod>> getAllPrescriptionPeriodTodayDelivery() {
        return service.paginationPrescriptionPeriodTodayDelivery();
    }

    @GetMapping("/today")
    public ResponseEntity<List<Prescription>> getAllToday() {
        return service.paginationToday();
    }

    /**
     * get Prescriptions of today per period
     */
    /**get all Prescription per period**/
    @GetMapping("/periods")
    public ResponseEntity<List<PrescriptionPeriod>> getAllPeriod() {
        return ResponseEntity.ok(service.allPeriod());
    }

    /**
     * periods
     **/
    @GetMapping("/periods/{start}/{end}")
    public ResponseEntity<List<PrescriptionPeriod>> getAllPeriodByDate(@PathVariable String start,@PathVariable String end) {

        PersianDate startDate=   PersianDate.parse(start);
        PersianDate endDate=   PersianDate.parse(end);
        List< PrescriptionPeriod > hits = service.allPeriod().stream()
                .filter(
                        prescription ->
                                (
                                        prescription.getStartDate().isAfter(startDate)
                                        || prescription.getStartDate().isEqual(startDate)
                                )
                                 &&
                                (
                                        prescription.getStartDate().isBefore(endDate)
                                        || prescription.getStartDate().isEqual(endDate)
                                )
                       )
                .collect( Collectors.toList()
                );

        return ResponseEntity.ok(hits);
    }

    /**
     *
     */
    @GetMapping("/periods/sum/{start}/{end}")
    public ResponseEntity<Map<Object, Double>> getAllSumPeriodByDate(@PathVariable String start,@PathVariable String end){
        PersianDate startDate=   PersianDate.parse(start);
        PersianDate endDate=   PersianDate.parse(end);

        List< PrescriptionPeriod > prescriptionFiltered = service.allPeriod().stream()
                .filter(
                        prescription ->
                                (
                                        prescription.getStartDate().isAfter(startDate)
                                                || prescription.getStartDate().isEqual(startDate)
                                )
                                        &&
                                        (
                                                prescription.getStartDate().isBefore(endDate)
                                                        || prescription.getStartDate().isEqual(endDate)
                                        )
                )
                .collect( Collectors.toList()
                );
        //prescriptionFiltered
        List<PrescriptionPeriodSum> prescriptionPeriodSums =new ArrayList<>();

//        prescriptionFiltered
//                .stream()
//                .collect(Collectors
//                        .groupingBy(
//                        pre -> new PrescriptionPeriodSum(pre.getMedicine().getId()),
//                        Collectors.summarizingDouble(pre -> pre.getDosePeriod())
//                        )
//                ).forEach((k,v) -> {
//            k.setDose(v.getSum());// = v.getSum();
//            System.out.println(v.getSum());
//            prescriptionPeriodSums.add(k);
//            System.out.println(k.getDose());
//        });

        Map<Object, Double> result =
                prescriptionFiltered.stream().collect(
                        Collectors.groupingBy(
                                pre -> pre.getMedicine().getName(), Collectors.summingDouble(pre -> pre.getDosePeriod())
                        )
                );


        // group by price, uses 'mapping' to convert List<Item> to Set<String>
//        Map<Double, Set<String>> result2 =
//                prescriptionFiltered.stream().collect(
//                        Collectors.groupingBy(pre->pre.getDose(),
//                                Collectors.mapping(PrescriptionPeriodSum::getMedicineId, Collectors.toSet())
//                        )
//                );
//
//        System.out.println(result2);


//        return ResponseEntity.ok(prescriptionFiltered);
        return ResponseEntity.ok(result);
//        return ResponseEntity.ok(prescriptionPeriodSums);
    }

    /***
     *
     */
    @GetMapping("/periods/download")
    public ResponseEntity<Resource> getPeriodsFile() {
        String filename = "periods_prescriptions.xlsx";
        InputStreamResource file = new InputStreamResource(service.excelPeriodsFileGenerate());

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(file);
    }
    //Delivery
    @PostMapping("/{id}/delivery")
    public ResponseEntity<Prescription> createDelivery(@PathVariable Long id) throws InvalidPrescriptionException {
        //set
        Prescription prescription = service.find(id);
        if(prescription.getMedicineDeliveries().size()>0){
            throw new InvalidPrescriptionException("this Prescription has Deliveries");
        }
          service.createDelivery(prescription);
        return ResponseEntity.ok(prescription);
    }

    /**
     * create a new Prescription
     */
    @PostMapping
    public ResponseEntity<Prescription> create(@Valid @RequestBody PrescriptionRequest request) throws Exception {

        log.info("Create a Prescription ");
        //get patient
        Patient patient = patientService.findFirst(request.getPatient());
        //get medicine
        Medicine medicine = medicineService.find(request.getMedicine());

        System.out.println(request.getDoseUnits().getCode());


        //set
        Prescription prescription = new Prescription();
        //Patient
        prescription.setPatient(patient);
        //startDate
        prescription.setStartDatePersian(request.getStartDatePersian());
        //expiryDate
        prescription.setEndDatePersian(request.getEndDatePersian());
        //Medicine
        prescription.setMedicine(medicine);
        //Units
        prescription.setDoseUnits(request.getDoseUnits());
        //dailyDose
        prescription.setDailyDose(request.getDailyDose());
        //treatmentPeriodDay
        prescription.setTreatmentPeriodDay(request.getTreatmentPeriodDay());
        //call create
//        service.savePrescription(prescription);
//        return ResponseEntity.ok(prescription) ;
        //temp
//        return ResponseEntity.ok(service.savePrescriptionWithDelivery(prescription));
        return ResponseEntity.ok(service.saveNewPrescription(prescription));
    }


    //update  rest api
    @PutMapping("/{id}")
    public ResponseEntity<Prescription> update(@PathVariable Long id, @RequestBody PrescriptionRequest request) throws InvalidPrescriptionException {
        //get patient
        Patient patient = patientService.findFirst(request.getPatient());
        //get medicine
        Medicine medicine = medicineService.find(request.getMedicine());


        //set
        Prescription prescription = new Prescription();
        //Patient
        prescription.setPatient(patient);
        //startDate
        prescription.setStartDatePersian(request.getStartDatePersian());
        //expiryDate
        prescription.setEndDatePersian(request.getEndDatePersian());
        //Medicine
        prescription.setMedicine(medicine);
        //Units
        prescription.setDoseUnits(request.getDoseUnits());
        //dailyDose
        prescription.setDailyDose(request.getDailyDose());
        //treatmentPeriodDay
        prescription.setTreatmentPeriodDay(request.getTreatmentPeriodDay());

        return ResponseEntity.ok(service.update(id,prescription));
    }

    // get  by id rest api
    @GetMapping("/{id}")
    public ResponseEntity<Prescription> getById(@PathVariable Long id) {
        return ResponseEntity.ok(service.find(id));
    }

    //delete  rest api
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> delete(@PathVariable Long id) {
        return ResponseEntity.ok(service.delete(id));
    }
}
