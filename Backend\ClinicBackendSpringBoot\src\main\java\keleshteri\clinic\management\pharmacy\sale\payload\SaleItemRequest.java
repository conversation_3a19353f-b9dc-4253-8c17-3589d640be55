package keleshteri.clinic.management.pharmacy.sale.payload;

import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.exception.InvalidDataException;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class SaleItemRequest {


    //ProductVariant
    @NotNull(message = "Please enter  productVariant")
     private Long productId;

    //Request_Quantity
    @NotNull(message = "Please enter  quantity")
    private Double quantity;

    //isConverted if convert quantity convert to new units
    private Boolean isConverted=false;

    //quantity converted
    @NotNull(message = "Please enter  quantity converted")
    private Double quantityConverted;

    //Units for  Converted Quantity
    private String unitsConverted;

    //unit_price
    @NotNull(message = "قیمت خرید واحد را وارد کنید")
    private BigDecimal unitPrice;

    //units_total
    private BigDecimal unitsTotal;

    //Discount
    private BigDecimal discount;

    //total
    private BigDecimal total;

    //Getter

    //ProductVariantId
    public Long getProductId() {
        if(productId==null){
            throw new InvalidDataException("لطفا کد کالا را وارد کنید");
        }
        return productId;
    }
    //Quantity
    public Double getQuantity() {
        if(quantity==null || quantity.doubleValue()==0){
            throw new InvalidDataException("لطفا تعداد وارد کنید");
        }
        return quantity;
    }
    //unitPrice
    public BigDecimal getUnitPrice() {
        if(unitPrice ==null || unitPrice.doubleValue()==0){
            throw new InvalidDataException("قیمت فروش واحد را وارد کنید");
        }
        return unitPrice;
    }

    //UnitsMeasurement
    public UnitsMeasurement getUnitsConverted() {

        System.out.println(unitsConverted);

        if(this.quantityConverted!=null && this.quantityConverted.doubleValue()>0 && unitsConverted.isEmpty()){
            throw new InvalidDataException("واحد تبدیل را وارد کنید");
        }
        if(unitsConverted ==null || unitsConverted.isEmpty()){
             return null;
        }

        return  Stream.of(UnitsMeasurement.values())
                .filter(
                        targetEnum -> targetEnum.name().equals(unitsConverted))
                .findFirst()
                .orElseThrow(()-> new InvalidDataException("واحد اندازه گیری   "+unitsConverted+" وارد شده با دیتا ما مطابقت ندارد. "));

    }
}
