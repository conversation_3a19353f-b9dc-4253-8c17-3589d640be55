package keleshteri.clinic.management.prescription;

import keleshteri.clinic.management.exception.InvalidPrescriptionException;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.patient.virtual.PrescriptionPeriod;
import keleshteri.clinic.management.prescription.payload.PrescriptionPaginationResponse;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;


public interface PrescriptionService {

//    public Prescription savePrescription(Prescription prescription) throws InvalidPrescriptionException;

    List<Prescription> all();

    List<Prescription> all(Patient patient);
    List<Prescription> allToday();

    List<PrescriptionPeriod> allPeriod();

    ResponseEntity<List<Prescription>> ExpiredToDayListPagination();

     Prescription savePrescriptionWithDelivery(Prescription prescription) throws InvalidPrescriptionException;

    ResponseEntity<PrescriptionPaginationResponse> pagination(int page, int size, String sortBy, String sortDir, 
        String fileNumber, String startDate, String medicineId);
    ResponseEntity<List<Prescription>> paginationToday();
    ResponseEntity<List<PrescriptionPeriod>> paginationPrescriptionPeriodToday();
    ResponseEntity<List<PrescriptionPeriod>> paginationPrescriptionPeriodTodayDelivery();
    ResponseEntity<List<Prescription>> paginationByPatient(Patient patient);

    Long countPrescription();

    Prescription update(Long id, Prescription prescription) throws InvalidPrescriptionException;

    Prescription find(Long id);

    Map<String, Boolean> delete(Long id);

    void createDelivery(Prescription prescription) throws InvalidPrescriptionException;

    Prescription saveNewPrescription (Prescription prescription) throws InvalidPrescriptionException;

    ByteArrayInputStream excelFileGenerate();

    ByteArrayInputStream excelPeriodsFileGenerate();


    ResponseEntity<Prescription> LastPrescriptionByPatient(Patient patient);
}
