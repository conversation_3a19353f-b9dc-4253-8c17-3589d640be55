package keleshteri.clinic.management.pharmacy.delivery.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.model.BaseEntity;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.prescription.Prescription;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.product.model.Product;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.*;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(
        name = "deliveries"
      )
public class Delivery  extends BaseEntity   {


    /**
     * delivery date
     */
    /** **/
    @Column(name = "delivery_date_persian")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "delivery date"
    )
    private String deliveryDatePersian;

    /** delivery Date Gregorian **/
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    /**
     * delivered Date
     */
    /** deliveredDatePersian **/
    @Column(name = "delivered_date_persian")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "delivered date"
    )
    private String deliveredDatePersian;

    /** deliveredDate Gregorian **/
    @Column(name = "delivered_date")
    private LocalDate deliveredDate;

    /**
     * Prescription
     */
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "prescription_id",nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnore
    private Prescription prescription;

    /** Dose for delivery **/
    @Column(name = "period_dose",nullable = false)
    private Double dose;

    /** DeliveredDose **/
    @Column(name = "delivered_dose",nullable = false)
    private Double deliveredDose=0.0;

    /** days **/
    @Column(name = "period_days")
    private int periodDays;

    //DoseLeft for next period
    @Column(name = "dose_left",nullable = false)
    private Double doseLeft=0.0;

    /** Is delivered true or false**/
    @Column(name = "delivered")
    private Boolean delivered=false;


    /**
     * product
     */
    @ManyToOne(fetch = FetchType.LAZY,optional = true)
    @JoinColumn(name = "product",nullable = true)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
//    @JsonIgnore
    private Product product;


    //medicine_deliveries
    @OneToMany(mappedBy = "delivery", cascade = CascadeType.ALL,orphanRemoval = true)
    private Set<DeliveryDetails>  deliveryDetails = new HashSet<>();

    /***
     * Setter Getter
     */
    /** Set Delivery Period Date Persian **/
    public void setDeliveryDatePersian(String deliveryDatePersian) {
        this.deliveryDate=PersianDate.parse(deliveryDatePersian).toGregorian();
        this.deliveryDatePersian = deliveryDatePersian;
    }
    /**DeliveredDatePersian**/
    public void setDeliveredDatePersian(String deliveredDatePersian) {
        this.deliveredDate=PersianDate.parse(deliveredDatePersian).toGregorian();
        this.deliveredDatePersian = deliveredDatePersian;
    }

    /***
     * Getter
     */
    public String getUnitLabel() {
        return prescription.getDoseUnitsLabel();
    }

    /** UnitsMeasurement **/
    public UnitsMeasurement getUnits(){
        return prescription.getDoseUnits();
    }

    /** **/
    public int getDeliveryDetailsCount() {
        return deliveryDetails.size();
    }

    /** Patient **/
    public Patient getPatient() {
        return prescription.getPatient();
    }
    /** Medicine **/
    public Medicine getMedicine() {
        return prescription.getMedicine();
    }


    /******
     * Constructor
     *****/
    public Delivery(
            String deliveryDatePersian,
            Prescription prescription,
            Double dose,
            Double doseLeft,
            int periodDays) {
        this.deliveryDatePersian = deliveryDatePersian;
        //localDate persianDate
        this.deliveryDate=PersianDate.parse(deliveryDatePersian).toGregorian();
        this.prescription = prescription;
        //
        this.dose = dose;
        this.periodDays = periodDays;
        this.doseLeft = doseLeft;
    }

    public Delivery(
            Product product,
            String deliveredDatePersian,
            Double deliveredDose,
            Boolean delivered
             ) {
        this.deliveredDatePersian = deliveredDatePersian;
        this.deliveredDate=PersianDate.parse(deliveredDatePersian).toGregorian();
        this.deliveredDose = deliveredDose;
        this.delivered = delivered;
        this.product = product;
    }
}
