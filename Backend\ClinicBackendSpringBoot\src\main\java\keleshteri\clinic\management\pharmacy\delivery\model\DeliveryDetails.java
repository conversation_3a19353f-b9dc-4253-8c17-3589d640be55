package keleshteri.clinic.management.pharmacy.delivery.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.github.mfathi91.time.PersianDate;
import keleshteri.clinic.management.model.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.*;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "delivery_details")
public class DeliveryDetails extends BaseEntity {

    //dailyDatePersian
    @Column(name = "daily_date_persian")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "daily date"
    )
    private String dailyDatePersian;

    //dailyDate
    @Column(name = "daily_date")
    private LocalDate dailyDate;

    //deliverDay
    @Column(name = "deliver_day")
    private boolean deliverDay=false;

    //deliveryPeriod
    @ManyToOne(fetch = FetchType.LAZY,optional = false)
    @JoinColumn(name = "delivery_id",nullable = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JsonIgnore
    private Delivery delivery;

    //DeliveredDose
    @Column(name = "dailyDose",nullable = false)
    private Double dose;

    //delivered
    @Column(name = "delivered")
    private boolean delivered=false;



    //set
    public void setDailyDatePersian(String dailyDatePersian) {
        PersianDate persianDate = PersianDate.parse(dailyDatePersian);
        //convert to Gregorian
        LocalDate DateGregorian = persianDate.toGregorian();
        this.dailyDate=DateGregorian;

        this.dailyDatePersian = dailyDatePersian;
    }

    //Constructor
    public DeliveryDetails(
            Delivery delivery,
            Double dose,
            String dailyDatePersian,
            boolean deliverDay) {
        this.dailyDatePersian = dailyDatePersian;
        //set
        //convert the String endDatePersian  to persianDate
        PersianDate persianDate = PersianDate.parse(dailyDatePersian);
        //convert to Gregorian
        LocalDate DateGregorian = persianDate.toGregorian();
        this.dailyDate=DateGregorian;

        this.deliverDay = deliverDay;
        this.delivery = delivery;
        this.dose = dose;

    }
}
