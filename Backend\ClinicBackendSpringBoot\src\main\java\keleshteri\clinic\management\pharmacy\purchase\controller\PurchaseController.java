package keleshteri.clinic.management.pharmacy.purchase.controller;

import keleshteri.clinic.management.pharmacy.purchase.model.Purchase;
import keleshteri.clinic.management.pharmacy.purchase.payload.PurchaseRequest;
import keleshteri.clinic.management.pharmacy.purchase.service.PurchaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@EnableTransactionManagement
@RequestMapping("/api/v1/medicine/inventory/purchases")
public class PurchaseController {

    @Autowired
    private PurchaseService service;

    // get all
    @GetMapping()
    public ResponseEntity<List<Purchase>>all(){
        return service.pagination();
    }

    // get Purchase  by id rest api
    @GetMapping("/{id}")
    public ResponseEntity<Purchase> getById(@PathVariable Long id) {
        return ResponseEntity.ok(service.find(id));
    }

    @PostMapping
    public ResponseEntity<Purchase> create(@Valid @RequestBody PurchaseRequest request){
        //send
        return ResponseEntity.ok(service.create(request));
    }



    //update Purchase
    @PutMapping("/{id}")
    public ResponseEntity<Purchase> update(@PathVariable Long id,@Valid @RequestBody PurchaseRequest request) {
        return ResponseEntity.ok(service.update(id, request));
    }
}
