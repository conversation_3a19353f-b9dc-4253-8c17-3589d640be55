package keleshteri.clinic.management.patient.payload;

import keleshteri.clinic.management.exception.InvalidDateException;
import lombok.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class ExpireDateCreatetorRequest {


    //treatmentPeriodDay
    @NotNull(message = "Please enter  treatment Period Day")
//    @Size(min=1,message="Please enter Size treatment Period Day")
    private int treatmentPeriodDay;

    //startDate
    @NotNull(message = "Please enter  start Date Persian")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "please enter date like that 1400-01-01"
    )
    private String startDatePersian;



    public int getTreatmentPeriodDay() {
        if(treatmentPeriodDay<=0){
            throw new InvalidDateException("please enter  treatment Period Day");
        }
        return treatmentPeriodDay;
    }
}
