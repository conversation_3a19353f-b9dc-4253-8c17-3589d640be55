package keleshteri.clinic.management.global;

import org.springframework.cglib.beans.BeanMap;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.Map;

public class BeanUtilsConvert extends org.springframework.beans.BeanUtils{

    public BeanUtilsConvert() {
    }

    /**
     * Instance of the object: the incoming class to instantiate an object of class
     *
     * @Param clazz class
     * @Return Object
     */
    public static <T> T newInstance(Class<?> clazz) {
        return (T) instantiateClass(clazz);
    }

    /**
     * Instantiate an object, passing the class name of the class to instantiate objects
     *
     * @Param clazzStr class name, you must pass the full path is passed, com ...
     * @Return Object
     */
    public static <T> T newInstance(String clazzStr) {
        try {
            Class<?> clazz = Class.forName(clazzStr);
            return newInstance(clazz);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException();
        }
    }

    /**
     * The encapsulated into objects form Map
     *
     * @Param src entity object needs to be encapsulated
     */
    public static Map toMap(Object src) {
        return BeanMap.create(src);
    }

    /**
     * Converted into a bean object Map
     *
     */
    public static <T> T toBean(Map<String, Object> beanMap, Class<T> valueType) {
        // object is instantiated
        T bean = BeanUtilsConvert.newInstance(valueType);
        PropertyDescriptor[] propertyDescriptors = getPropertyDescriptors(valueType);
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            String properName = propertyDescriptor.getName();
            // filter class attribute
            if ("class".equals(properName)) {
                continue;
            }
            if (beanMap.containsKey(properName)) {
                Method writeMethod = propertyDescriptor.getWriteMethod();
                if (null == writeMethod) {
                    continue;
                }
                Object value = beanMap.get(properName);
                if (!writeMethod.isAccessible()) {
                    writeMethod.setAccessible(true);
                }
                try {
                    writeMethod.invoke(bean, value);
                } catch (Throwable throwable) {
                    throw new RuntimeException("Could not set property '" + properName + " ' to bean" + throwable);
                }
            }
        }
        return bean;
    }

}
