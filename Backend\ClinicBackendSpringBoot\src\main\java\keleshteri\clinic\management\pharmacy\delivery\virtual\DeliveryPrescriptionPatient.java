package keleshteri.clinic.management.pharmacy.delivery.virtual;

import keleshteri.clinic.management.enums.UnitsMeasurement;
import keleshteri.clinic.management.patient.model.Patient;
import keleshteri.clinic.management.pharmacy.medicine.model.Medicine;
import keleshteri.clinic.management.product.model.Product;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeliveryPrescriptionPatient {

    //Patient
    private Patient patient;

    //prescriptionStartDatePersian
    private String prescriptionStartDatePersian;

    //prescription endDate
    private  String prescriptionEndDatePersian;

    //prescription Expired
    private Boolean prescriptionExpired=false;

    //PrescriptionMedicine
    private Medicine prescriptionMedicine;

    //UnitsPrescription
    private UnitsMeasurement prescriptionDoseUnits;

    //prescriptionDailyDose
    private Double prescriptionDailyDose;

    //prescriptionDosePeriod
    private Double prescriptionDosePeriod;

    //prescriptionTreatmentPeriodDay
    private int prescriptionTreatmentPeriodDay;

    //prescriptionDelivery each  Period Days)
    private int prescriptionDeliveryPeriodDay=14;

    //prescriptionDeliveredDose
    private Double prescriptionDeliveredDoses;

    //prescriptionDelivered
    private Boolean prescriptionDelivered;

    /**
     * delivery
     */
    //periodDatePersian
    private String deliveryPeriodDatePersian;

    //deliveredDatePersian
    private String deliveredDatePersian;

    //Dose for delivery
    private Double deliveryDose;

    //DeliveredDose
    private Double deliveredDose;

    //days
    private int deliveryPeriodDays;

    //DoseLeft for next period
    private Double deliveryDoseLeft;

    //delivered
    private Boolean delivered;

     // product
     private Product product;

    private Long productId;

    /***
     * Constructor
     ***/

    public DeliveryPrescriptionPatient(
            Patient patient,
            String prescriptionStartDatePersian,
            String prescriptionEndDatePersian,
            Boolean prescriptionExpired,
            Medicine prescriptionMedicine,
            UnitsMeasurement prescriptionDoseUnits,
            Double prescriptionDailyDose,
            Double prescriptionDosePeriod,
            int prescriptionTreatmentPeriodDay,
            int prescriptionDeliveryPeriodDay,
            Double prescriptionDeliveredDoses,
            Boolean prescriptionDelivered,
            String deliveryPeriodDatePersian,
            String deliveredDatePersian,
            Double deliveryDose,
            Double deliveredDose,
            int deliveryPeriodDays,
            Double deliveryDoseLeft,
            Boolean delivered,
            Long  productId
            ) {
        this.patient = patient;
        this.prescriptionStartDatePersian = prescriptionStartDatePersian;
        this.prescriptionEndDatePersian = prescriptionEndDatePersian;
        this.prescriptionExpired = prescriptionExpired;
        this.prescriptionMedicine = prescriptionMedicine;
        this.prescriptionDoseUnits = prescriptionDoseUnits;
        this.prescriptionDailyDose = prescriptionDailyDose;
        this.prescriptionDosePeriod = prescriptionDosePeriod;
        this.prescriptionTreatmentPeriodDay = prescriptionTreatmentPeriodDay;
        this.prescriptionDeliveryPeriodDay = prescriptionDeliveryPeriodDay;
        this.prescriptionDeliveredDoses = prescriptionDeliveredDoses;
        this.prescriptionDelivered = prescriptionDelivered;
        this.deliveryPeriodDatePersian = deliveryPeriodDatePersian;
        this.deliveredDatePersian = deliveredDatePersian;
        this.deliveryDose = deliveryDose;
        this.deliveredDose = deliveredDose;
        this.deliveryPeriodDays = deliveryPeriodDays;
        this.deliveryDoseLeft = deliveryDoseLeft;
        this.delivered = delivered;
        this.productId= productId;

    }




    public DeliveryPrescriptionPatient(
            Patient patient,
            String prescriptionStartDatePersian,
            String prescriptionEndDatePersian,
            Boolean prescriptionExpired,
            Medicine prescriptionMedicine,
            UnitsMeasurement prescriptionDoseUnits,
            Double prescriptionDailyDose,
            Double prescriptionDosePeriod,
            int prescriptionTreatmentPeriodDay,
            int prescriptionDeliveryPeriodDay,
            Double prescriptionDeliveredDoses,
            Boolean prescriptionDelivered,
            String deliveryPeriodDatePersian,
            String deliveredDatePersian,
            Double deliveryDose,
            Double deliveredDose,
            int deliveryPeriodDays,
            Double deliveryDoseLeft,
            Boolean delivered,
            Product product) {
        this.patient = patient;
        this.prescriptionStartDatePersian = prescriptionStartDatePersian;
        this.prescriptionEndDatePersian = prescriptionEndDatePersian;
        this.prescriptionExpired = prescriptionExpired;
        this.prescriptionMedicine = prescriptionMedicine;
        this.prescriptionDoseUnits = prescriptionDoseUnits;
        this.prescriptionDailyDose = prescriptionDailyDose;
        this.prescriptionDosePeriod = prescriptionDosePeriod;
        this.prescriptionTreatmentPeriodDay = prescriptionTreatmentPeriodDay;
        this.prescriptionDeliveryPeriodDay = prescriptionDeliveryPeriodDay;
        this.prescriptionDeliveredDoses = prescriptionDeliveredDoses;
        this.prescriptionDelivered = prescriptionDelivered;
        this.deliveryPeriodDatePersian = deliveryPeriodDatePersian;
        this.deliveredDatePersian = deliveredDatePersian;
        this.deliveryDose = deliveryDose;
        this.deliveredDose = deliveredDose;
        this.deliveryPeriodDays = deliveryPeriodDays;
        this.deliveryDoseLeft = deliveryDoseLeft;
        this.delivered = delivered;
        this.product = product;
    }

    /***
     * Getter
     */
    public String getPatient() {
        return patient.getFirstName()+" "+patient.getLastName();
    }

    public String getPrescriptionMedicine() {
        return prescriptionMedicine.getName();
    }

    public String getPrescriptionDoseUnits() {
        return prescriptionDoseUnits.getLabel();
    }

    public String getProduct() {
        if(product!=null){
            return product.getName();
        }
        return null;
    }
}
