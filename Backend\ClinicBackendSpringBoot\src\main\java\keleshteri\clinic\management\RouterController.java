package keleshteri.clinic.management;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

@RestController
public class RouterController {

    @RequestMapping("/**/{path:[^.]*}")
    public ModelAndView redirect() {
        return new ModelAndView("forward:/");
    }

    @RequestMapping(value = {"/{path:^(?!api|public|ws)[^\\.]*}", "/**/{path:^(?!api|public|ws).*}/{path:[^\\.]*}"})
    public ModelAndView get(){
//        return "index";
        return new ModelAndView("forward:/");
    }
}
