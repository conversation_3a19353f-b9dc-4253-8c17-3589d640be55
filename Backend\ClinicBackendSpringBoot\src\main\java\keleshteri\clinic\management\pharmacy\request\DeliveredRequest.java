package keleshteri.clinic.management.pharmacy.request;

import keleshteri.clinic.management.pharmacy.delivery.model.DeliveryDetails;
import lombok.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class DeliveredRequest {

    //deliveryPeriod
    @NotNull(message = "لطفا کد تحویل را وارد کنید")
    private Long delivery;

    //doseDelivered
    @NotNull(message = "لطفا  دوز تحویل  را وارد کنید")
    private Double doseDelivered;

    //ProductVariant
    @NotNull(message = "Please enter  product")
    private Long product;

    //Request_Quantity
    @NotNull(message = "Please enter  quantity")
    private Double quantity;

    //DatePersian
    @NotNull(message = "لطفا  تاریخ تحویل  را وارد کنید")
    @Pattern(
            regexp ="^[1-4]\\d{3}\\-((0[1-6]\\-((3[0-1])|([1-2][0-9])|(0[1-9])))|((1[0-2]|(0[7-9]))\\-(30|31|([1-2][0-9])|(0[1-9]))))$",
            message = "Delivered date"
    )
    private String deliveredDatePersian;

    //inventoryPeriod
    @NotNull(message = "Please enter  inventoryPeriod")
    private  Long inventoryPeriod;



    @NotNull(message = "روز های تحویل را وارد کنید")
    private List<DeliveryDetails> deliveryDetails;


}
